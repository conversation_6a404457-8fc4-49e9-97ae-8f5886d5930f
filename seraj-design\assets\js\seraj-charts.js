/**
 * Seraj Scientific Database Charts
 * Interactive charts for statistics dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Chart.js default configuration
    Chart.defaults.font.family = 'Vazirmatn, sans-serif';
    Chart.defaults.color = '#64748b';
    
    // Colors for charts
    const colors = {
        primary: '#C4EE18',
        secondary: '#10302a',
        accent: '#405955',
        gradient: {
            primary: ['#C4EE18', '#10302a'],
            secondary: ['#405955', '#64748b']
        }
    };

    // Bar Chart - Annual Research Publications
    const barCtx = document.getElementById('serajBarChart');
    if (barCtx) {
        const barChart = new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: ['۱۳۹۹', '۱۴۰۰', '۱۴۰۱', '۱۴۰۲', '۱۴۰۳'],
                datasets: [{
                    label: 'تعداد مقالات',
                    data: [28500, 32100, 35800, 41200, 45600],
                    backgroundColor: function(context) {
                        const chart = context.chart;
                        const {ctx, chartArea} = chart;
                        if (!chartArea) return null;
                        
                        const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                        gradient.addColorStop(0, colors.primary);
                        gradient.addColorStop(1, colors.secondary);
                        return gradient;
                    },
                    borderColor: colors.secondary,
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1e293b',
                        bodyColor: '#64748b',
                        borderColor: colors.primary,
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y.toLocaleString('fa-IR') + ' مقاله';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(196, 238, 24, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            callback: function(value) {
                                return (value / 1000).toLocaleString('fa-IR') + 'K';
                            },
                            color: '#64748b',
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#64748b',
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // Doughnut Chart - Scientific Fields Distribution
    const doughnutCtx = document.getElementById('serajDoughnutChart');
    if (doughnutCtx) {
        const doughnutChart = new Chart(doughnutCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    'مهندسی و فناوری',
                    'علوم پزشکی',
                    'علوم پایه',
                    'علوم انسانی',
                    'کشاورزی',
                    'سایر'
                ],
                datasets: [{
                    data: [28, 22, 18, 15, 10, 7],
                    backgroundColor: [
                        '#C4EE18',
                        '#10302a',
                        '#405955',
                        '#64748b',
                        '#94a3b8',
                        '#cbd5e1'
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 3,
                    hoverBorderWidth: 4,
                    hoverOffset: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#64748b'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1e293b',
                        bodyColor: '#64748b',
                        borderColor: colors.primary,
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                },
                cutout: '60%',
                animation: {
                    animateRotate: true,
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // Animate progress bars when they come into view
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -50px 0px'
    };

    const progressObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBars = entry.target.querySelectorAll('.seraj-progress-bar');
                progressBars.forEach((bar, index) => {
                    setTimeout(() => {
                        bar.style.transform = 'scaleX(1)';
                        bar.style.transformOrigin = 'left';
                        bar.style.transition = 'transform 1s ease-out';
                    }, index * 200);
                });
                progressObserver.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe ranking cards
    const rankingCards = document.querySelectorAll('.seraj-ranking-card');
    rankingCards.forEach(card => {
        // Initially scale progress bars to 0
        const progressBars = card.querySelectorAll('.seraj-progress-bar');
        progressBars.forEach(bar => {
            bar.style.transform = 'scaleX(0)';
        });
        
        progressObserver.observe(card);
    });

    // Add hover effects to chart cards
    const chartCards = document.querySelectorAll('.seraj-chart-card');
    chartCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-4px)';
        });
    });

    // Add click functionality to ranking items
    const rankingItems = document.querySelectorAll('.seraj-ranking-item');
    rankingItems.forEach(item => {
        item.addEventListener('click', function() {
            // Add pulse animation
            this.style.animation = 'pulse 0.6s ease-in-out';
            setTimeout(() => {
                this.style.animation = '';
            }, 600);
        });
    });
});

// CSS animation for pulse effect
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);
