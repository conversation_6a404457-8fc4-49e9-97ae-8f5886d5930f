// Seraj Scientific Database - Main JavaScript

// Language translations
const translations = {
    fa: {
        // Navigation
        'current-lang': 'فارسی',
        'login-text': 'ورود / ثبت نام',
        
        // Hero Section
        'hero-title': 'بزرگترین پایگاه داده علمی ایران',
        'hero-subtitle': 'دسترسی به بیش از ۱۳۵ میلیون مدرک علمی شامل مقالات، کتاب‌ها، پایان‌نامه‌ها و منابع تحقیقاتی',
        'search-placeholder': 'جستجوی مقالات، نویسندگان، موضوعات...',
        'search-input-mobile': 'جستجو در سراج...',
        
        // Stats Section
        'total-documents': 'کل مدارک',
        'total-articles': 'مقالات',
        'total-patents': 'اختراعات',
        
        // Tutorial Section
        'tutorial-title': 'چگونه از سراج استفاده کنیم؟',
        'tutorial-subtitle': 'در چند مرحله ساده به منابع علمی دسترسی پیدا کنید',
        'step-1': 'مرحله ۱',
        'step-2': 'مرحله ۲',
        'step-3': 'مرحله ۳',
        'step-4': 'مرحله ۴',
        'register-login': 'ثبت نام یا ورود',
        'register-desc': 'حساب کاربری خود را ایجاد کنید یا وارد سراج شوید',
        'search': 'جستجو',
        'search-desc': 'موضوع مورد نظر خود را جستجو کنید',
        'filter': 'فیلتر',
        'filter-desc': 'نتایج را برای یافتن بهترین منابع فیلتر کنید',
        'save': 'ذخیره',
        'save-desc': 'نتایج مورد نظر خود را ذخیره کنید',

        // Quick Filters
        'articles': 'مقالات',
        'researchers': 'پژوهشگران',
        'universities': 'دانشگاه‌ها',
        'access-text': 'دسترسی به بیش از ۱۳۵ میلیون مدرک علمی',
        
        // Top Section
        'top-title': 'برترین‌ها در سراج',
        'top-subtitle': 'پژوهشگران، موسسات و کشورهای پیشرو در علم',
        'top-researchers-title': 'پژوهشگران برتر',
        'top-institutions-title': 'موسسات برتر',
        'top-countries-title': 'کشورهای برتر',
        'papers-count': 'مقاله',
        
        // Statistics Section
        'stats-title': 'آمار و تجسم داده‌ها',
        'stats-subtitle': 'نمایش آماری از دسته‌بندی‌ها و موضوعات تحقیقاتی',
        'categories-chart-title': 'دسته‌بندی‌های کلی',
        'topics-chart-title': 'موضوعات تخصصی',
        'publications-chart-title': 'انتشارات سالانه',
        'impact-chart-title': 'تأثیرگذاری تحقیقات',
        'export-charts': 'دانلود نمودارها',
        'refresh-charts': 'بروزرسانی',
        'animate-charts': 'انیمیشن',

        // Footer
        'footer-about-title': 'درباره سراج',
        'footer-about-text': 'سراج بزرگترین پایگاه داده علمی ایران است که دسترسی به میلیون‌ها مدرک علمی را فراهم می‌کند.',
        'footer-services-title': 'خدمات',
        'footer-support-title': 'پشتیبانی',
        'footer-contact-title': 'تماس با ما',
        'footer-copyright': '© ۲۰۲۴ سراج. تمامی حقوق محفوظ است.',
        
        // Footer Links
        'advanced-search': 'جستجوی پیشرفته',
        'download-articles': 'دانلود مقالات',
        'citation-analysis': 'تحلیل استنادی',
        'research-tracking': 'پیگیری تحقیقات',
        'user-guide': 'راهنمای استفاده',
        'faq': 'سوالات متداول',
        'contact-us': 'تماس با ما',
        'report-issue': 'گزارش مشکل',
        'privacy': 'حریم خصوصی',

        // Messages
        'search-loading': 'در حال جستجو...',
        'login-development': 'صفحه ورود/ثبت نام در حال توسعه است',
        'search-for': 'جستجو برای:',
        
        // Logo
        'logo': 'سراج',
        
        // Papers Count
        'papers-count': 'مقاله',
        
        // Countries
        'china': 'چین',
        'usa': 'آمریکا',
        'germany': 'آلمان',
        'uk': 'انگلستان',
        
        // Contact Info
        'phone': '۰۲۱-۱۲۳۴۵۶۷۸',
        'address': 'تهران، ایران',

        // Top Rankings Actions
        'view-profile': 'مشاهده پروفایل',
        'view-papers': 'مقالات',
        'view-institution': 'مشاهده موسسه',
        'view-researchers': 'پژوهشگران',
        'view-country': 'مشاهده کشور',
        'view-stats': 'آمار',

        // Additional Services
        'api-access': 'دسترسی API',
        'bulk-download': 'دانلود انبوه',
        'tutorials': 'آموزش‌ها',
        'webinars': 'وبینارها',

        // Legal Pages
        'terms': 'شرایط استفاده',
        'cookies': 'کوکی‌ها',
        'accessibility': 'دسترسی‌پذیری',

        // Contact Modal
        'contact-modal-title': 'تماس با ما',
        'contact-form-title': 'فرم تماس',
        'name': 'نام',
        'email': 'ایمیل',
        'subject': 'موضوع',
        'message': 'پیام',
        'send-message': 'ارسال پیام',
        'select-subject': 'انتخاب موضوع',
        'technical-support': 'پشتیبانی فنی',
        'account-issues': 'مشکلات حساب کاربری',
        'feedback': 'بازخورد',
        'partnership': 'همکاری',
        'other': 'سایر',
        'contact-info-title': 'اطلاعات تماس',
        'email-addresses': 'آدرس‌های ایمیل',
        'phone-numbers': 'شماره‌های تماس',
        'main-phone': 'دفتر مرکزی: ۰۲۱-۱۲۳۴۵۶۷۸',
        'support-phone-full': 'پشتیبانی: ۰۲۱-۸۷۶۵۴۳۲۱',
        'working-hours': 'ساعات کاری',
        'weekdays': 'شنبه تا چهارشنبه: ۸:۰۰ - ۱۷:۰۰',
        'thursday': 'پنج‌شنبه: ۸:۰۰ - ۱۳:۰۰'
    },
    en: {
        // Navigation
        'current-lang': 'English',
        'login-text': 'Login / Register',
        
        // Hero Section
        'hero-title': 'Iran\'s Largest Scientific Database',
        'hero-subtitle': 'Access to over 135 million scientific documents including articles, books, theses and research resources',
        'search-placeholder': 'Search articles, authors, topics...',
        'search-input-mobile': 'Search in Seraj...',
        
        // Stats Section
        'total-documents': 'Total Documents',
        'total-articles': 'Articles',
        'total-patents': 'Patents',
        
        // Tutorial Section
        'tutorial-title': 'How to Use Seraj?',
        'tutorial-subtitle': 'Access scientific resources in a few simple steps',
        'step-1': 'Step 1',
        'step-2': 'Step 2',
        'step-3': 'Step 3',
        'step-4': 'Step 4',
        'register-login': 'Register or Login',
        'register-desc': 'Create your account or login to Seraj',
        'search': 'Search',
        'search-desc': 'Search for your desired topic',
        'filter': 'Filter',
        'filter-desc': 'Filter results to find the best resources',
        'save': 'Save',
        'save-desc': 'Save your desired results',

        // Quick Filters
        'articles': 'Articles',
        'researchers': 'Researchers',
        'universities': 'Universities',
        'access-text': 'Access to over 135 million scientific documents',
        
        // Top Section
        'top-title': 'Top in Seraj',
        'top-subtitle': 'Leading researchers, institutions and countries in science',
        'top-researchers-title': 'Top Researchers',
        'top-institutions-title': 'Top Institutions',
        'top-countries-title': 'Top Countries',
        'papers-count': 'papers',
        
        // Statistics Section
        'stats-title': 'Statistics and Data Visualization',
        'stats-subtitle': 'Statistical display of research categories and topics',
        'categories-chart-title': 'General Categories',
        'topics-chart-title': 'Specialized Topics',
        'publications-chart-title': 'Annual Publications',
        'impact-chart-title': 'Research Impact',
        'export-charts': 'Export Charts',
        'refresh-charts': 'Refresh',
        'animate-charts': 'Animate',

        // Footer
        'footer-about-title': 'About Seraj',
        'footer-about-text': 'Seraj is Iran\'s largest scientific database providing access to millions of scientific documents.',
        'footer-services-title': 'Services',
        'footer-support-title': 'Support',
        'footer-contact-title': 'Contact Us',
        'footer-copyright': '© 2024 Seraj. All rights reserved.',
        
        // Footer Links
        'advanced-search': 'Advanced Search',
        'download-articles': 'Download Articles',
        'citation-analysis': 'Citation Analysis',
        'research-tracking': 'Research Tracking',
        'user-guide': 'User Guide',
        'faq': 'FAQ',
        'contact-us': 'Contact Us',
        'report-issue': 'Report Issue',
        'privacy': 'Privacy Policy',
        'terms': 'Terms of Use',

        // Messages
        'search-loading': 'Searching...',
        'login-development': 'Login/Register page is under development',
        'search-for': 'Search for:',
        
        // Logo
        'logo': 'Seraj',
        
        // Papers Count
        'papers-count': 'papers',
        
        // Countries
        'china': 'China',
        'usa': 'United States',
        'germany': 'Germany',
        'uk': 'United Kingdom',
        
        // Contact Info
        'phone': '+98-21-********',
        'address': 'Tehran, Iran',

        // Top Rankings Actions
        'view-profile': 'View Profile',
        'view-papers': 'Papers',
        'view-institution': 'View Institution',
        'view-researchers': 'Researchers',
        'view-country': 'View Country',
        'view-stats': 'Statistics',

        // Additional Services
        'api-access': 'API Access',
        'bulk-download': 'Bulk Download',
        'tutorials': 'Tutorials',
        'webinars': 'Webinars',

        // Legal Pages
        'terms': 'Terms of Use',
        'cookies': 'Cookies',
        'accessibility': 'Accessibility',

        // Contact Modal
        'contact-modal-title': 'Contact Us',
        'contact-form-title': 'Contact Form',
        'name': 'Name',
        'email': 'Email',
        'subject': 'Subject',
        'message': 'Message',
        'send-message': 'Send Message',
        'select-subject': 'Select Subject',
        'technical-support': 'Technical Support',
        'account-issues': 'Account Issues',
        'feedback': 'Feedback',
        'partnership': 'Partnership',
        'other': 'Other',
        'contact-info-title': 'Contact Information',
        'email-addresses': 'Email Addresses',
        'phone-numbers': 'Phone Numbers',
        'main-phone': 'Main Office: +98-21-********',
        'support-phone-full': 'Support: +98-21-********',
        'working-hours': 'Working Hours',
        'weekdays': 'Saturday to Wednesday: 8:00 - 17:00',
        'thursday': 'Thursday: 8:00 - 13:00'
    }
};

// Current language
let currentLanguage = 'fa';

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });

    // Initialize components
    initializeNavbar();
    initializeSearch();
    initializeCounters();
    initializeLanguageSwitch();
    initializeScrollEffects();
    initializeLoadingSkeletons();

    // Set initial language
    updateLanguage(currentLanguage);
});

// Initialize Navbar
function initializeNavbar() {
    const navbar = document.querySelector('.navbar');

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Login button functionality
    const loginBtn = document.getElementById('login-btn');

    if (loginBtn) {
        loginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleLogin();
        });
    }
}

// Handle Login Action
function handleLogin() {
    showToast('صفحه ورود/ثبت نام در حال توسعه است', 'info');
}

// Initialize Search
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');

    if (searchInput && searchBtn) {
        // Set responsive placeholder
        updateSearchPlaceholder();

        // Update placeholder on window resize
        window.addEventListener('resize', updateSearchPlaceholder);

        // Search button click
        searchBtn.addEventListener('click', function() {
            performSearch();
        });

        // Enter key press
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Search suggestions (placeholder for future implementation)
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length > 2) {
                // Show search suggestions
                showSearchSuggestions(query);
            } else {
                hideSearchSuggestions();
            }
        });
    }
}

// Update Search Placeholder based on screen size
function updateSearchPlaceholder() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        const isMobile = window.innerWidth <= 767;
        const placeholderKey = isMobile ? 'search-input-mobile' : 'search-placeholder';

        if (translations[currentLanguage] && translations[currentLanguage][placeholderKey]) {
            searchInput.placeholder = translations[currentLanguage][placeholderKey];
        }
    }
}



// Show Search Suggestions
function showSearchSuggestions(query) {
    // Placeholder for search suggestions
    // TODO: Implement search suggestions
}

// Hide Search Suggestions
function hideSearchSuggestions() {
    // Placeholder for hiding suggestions
    // TODO: Implement hiding suggestions
}

// Initialize Counters
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-number');

    // Intersection Observer for counters
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.5
    });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Animate Counter
function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60 FPS
    let current = 0;

    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }

        // Format number with commas
        element.textContent = formatNumber(Math.floor(current));
    }, 16);
}

// Format Number with Commas
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// Initialize Language Switch
function initializeLanguageSwitch() {
    const languageDropdown = document.getElementById('languageDropdown');

    if (languageDropdown) {
        languageDropdown.addEventListener('click', function(e) {
            e.preventDefault();
        });
    }

    // Load saved language preference
    const savedLanguage = localStorage.getItem('seraj-language');
    if (savedLanguage && savedLanguage !== currentLanguage) {
        currentLanguage = savedLanguage;
        updateLanguage(savedLanguage);
        updateLanguageToggleState(savedLanguage);
    }

    // Add keyboard navigation for language switcher
    document.addEventListener('keydown', function(e) {
        if (e.altKey && e.key === 'l') {
            e.preventDefault();
            const dropdown = document.getElementById('languageDropdown');
            if (dropdown) {
                dropdown.click();
            }
        }
    });
}

// Update Language Toggle State
function updateLanguageToggleState(lang) {
    const languageOptions = document.querySelectorAll('.language-option');

    languageOptions.forEach(option => {
        const optionLang = option.getAttribute('data-lang');
        if (optionLang === lang) {
            option.classList.add('active');
            option.style.background = 'rgba(30, 58, 138, 0.1)';
            option.style.color = 'var(--primary-color)';
        } else {
            option.classList.remove('active');
            option.style.background = '';
            option.style.color = '';
        }
    });
}

// Change Language with Enhanced Animation
function changeLanguage(lang) {
    if (currentLanguage === lang) return; // Don't change if already selected

    // Add loading state
    const languageToggle = document.querySelector('.language-toggle');
    const originalContent = languageToggle.innerHTML;

    // Show loading spinner
    languageToggle.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Loading...</span>';
    languageToggle.style.pointerEvents = 'none';

    // Add fade effect to content
    document.body.style.transition = 'opacity 0.3s ease';
    document.body.style.opacity = '0.7';

    setTimeout(() => {
        currentLanguage = lang;
        updateLanguage(lang);

        // Update HTML attributes
        document.documentElement.lang = lang;
        document.documentElement.dir = lang === 'fa' ? 'rtl' : 'ltr';

        // Update body class for styling
        document.body.className = document.body.className.replace(/lang-\w+/, '');
        document.body.classList.add(`lang-${lang}`);

        // Store language preference
        localStorage.setItem('seraj-language', lang);

        // Restore content and remove loading state
        setTimeout(() => {
            languageToggle.innerHTML = originalContent;
            languageToggle.style.pointerEvents = 'auto';
            document.body.style.opacity = '1';

            // Update language toggle appearance
            updateLanguageToggleState(lang);

            // Show success message
            showToast(
                lang === 'fa' ? 'زبان با موفقیت تغییر کرد' : 'Language changed successfully',
                'success'
            );
        }, 300);
    }, 500);
}

// Update Language
function updateLanguage(lang) {
    const elements = document.querySelectorAll('[data-translate]');
    
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        if (translations[lang] && translations[lang][key]) {
            if (element.tagName === 'INPUT' && element.type === 'text') {
                element.placeholder = translations[lang][key];
            } else {
                element.textContent = translations[lang][key];
            }
        }
    });

    // Update current language display
    const currentLangElement = document.getElementById('current-lang');
    if (currentLangElement) {
        currentLangElement.textContent = translations[lang]['current-lang'];
    }

    // Update login text
    const loginTextElement = document.getElementById('login-text');
    if (loginTextElement) {
        loginTextElement.textContent = translations[lang]['login-text'];
    }

    // Update search placeholder
    updateSearchPlaceholder();

    // Update document direction
    document.documentElement.dir = lang === 'fa' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;

    // Update charts language if available
    if (window.SerajCharts && typeof window.SerajCharts.updateChartsLanguage === 'function') {
        window.SerajCharts.updateChartsLanguage(lang);
    }
}

// Initialize Loading Skeletons
function initializeLoadingSkeletons() {
    // Create intersection observer for skeleton sections
    const skeletonObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const section = entry.target;
                const sectionType = getSectionType(section);

                if (sectionType) {
                    // Start loading animation for this section
                    startSectionLoading(sectionType);
                    // Stop observing this section
                    skeletonObserver.unobserve(section);
                }
            }
        });
    }, {
        threshold: 0.2, // Trigger when 20% of section is visible
        rootMargin: '50px 0px' // Start loading 50px before section comes into view
    });

    // Observe all sections with skeletons using data attributes
    const sectionsToObserve = document.querySelectorAll('[data-skeleton-section]');

    sectionsToObserve.forEach(section => {
        if (section) {
            skeletonObserver.observe(section);
        }
    });

    // Show stats immediately since it's in hero section
    setTimeout(() => {
        showContent('stats');
    }, 1500);
}

// Get section type from element
function getSectionType(element) {
    // Use data attribute for cleaner detection
    const sectionType = element.getAttribute('data-skeleton-section');
    if (sectionType) {
        return sectionType;
    }

    // Fallback to old method
    if (element.classList.contains('stats-container')) {
        return 'stats';
    }
    if (element.querySelector('.skeleton-researchers')) {
        return 'researchers';
    }
    if (element.querySelector('.skeleton-institutions')) {
        return 'institutions';
    }
    if (element.querySelector('.skeleton-countries')) {
        return 'countries';
    }
    if (element.id === 'statistics') {
        return 'charts';
    }

    return null;
}

// Start loading animation for specific section
function startSectionLoading(sectionType) {
    const loadingDuration = 2000; // 2 seconds loading time

    // Add visual indicator that loading has started
    const skeletonElement = document.querySelector(`.skeleton-${sectionType}`);
    if (skeletonElement) {
        skeletonElement.classList.add('loading-active');
    }

    setTimeout(() => {
        showContent(sectionType);
    }, loadingDuration);
}

// Show Content and Hide Skeleton
function showContent(section) {
    const skeletonElement = document.querySelector(`.skeleton-${section}`);
    const contentElement = document.querySelector(`.${section}-content`);

    if (skeletonElement && contentElement) {
        // Fade out skeleton
        skeletonElement.style.transition = 'opacity 0.3s ease';
        skeletonElement.style.opacity = '0';

        setTimeout(() => {
            skeletonElement.style.display = 'none';
            contentElement.style.display = 'block';

            // Fade in content
            contentElement.style.opacity = '0';
            contentElement.style.transition = 'opacity 0.5s ease';

            setTimeout(() => {
                contentElement.style.opacity = '1';

                // Trigger animations for the newly shown content
                if (section === 'stats') {
                    initializeCounters();
                }
            }, 50);
        }, 300);
    }

    // Special handling for charts
    if (section === 'charts') {
        const categoriesSkeletonElement = document.querySelector('.skeleton-categories-chart');
        const categoriesContentElement = document.querySelector('.categories-chart-content');
        const topicsSkeletonElement = document.querySelector('.skeleton-topics-chart');
        const topicsContentElement = document.querySelector('.topics-chart-content');

        // Show categories chart
        if (categoriesSkeletonElement && categoriesContentElement) {
            categoriesSkeletonElement.style.transition = 'opacity 0.3s ease';
            categoriesSkeletonElement.style.opacity = '0';

            setTimeout(() => {
                categoriesSkeletonElement.style.display = 'none';
                categoriesContentElement.style.display = 'block';
                categoriesContentElement.style.opacity = '0';
                categoriesContentElement.style.transition = 'opacity 0.5s ease';

                setTimeout(() => {
                    categoriesContentElement.style.opacity = '1';
                }, 50);
            }, 300);
        }

        // Show topics chart
        if (topicsSkeletonElement && topicsContentElement) {
            topicsSkeletonElement.style.transition = 'opacity 0.3s ease';
            topicsSkeletonElement.style.opacity = '0';

            setTimeout(() => {
                topicsSkeletonElement.style.display = 'none';
                topicsContentElement.style.display = 'block';
                topicsContentElement.style.opacity = '0';
                topicsContentElement.style.transition = 'opacity 0.5s ease';

                setTimeout(() => {
                    topicsContentElement.style.opacity = '1';
                }, 50);
            }, 300);
        }
    }
}

// Initialize Scroll Effects
function initializeScrollEffects() {
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Parallax effect for hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroSection = document.querySelector('.hero-section');

        if (heroSection) {
            const rate = scrolled * -0.5;
            heroSection.style.transform = `translateY(${rate}px)`;
        }
    });
}

// Stats Section Animation
const initStatsAnimation = () => {
    const statsSection = document.querySelector('.stats-section');
    if (!statsSection) return;

    const showStatsOnScroll = () => {
        const triggerBottom = window.innerHeight * 0.8;
        const statsSectionTop = statsSection.getBoundingClientRect().top;

        if (statsSectionTop < triggerBottom) {
            statsSection.classList.add('visible');
            startCountingAnimation();
            window.removeEventListener('scroll', showStatsOnScroll);
        }
    };

    const startCountingAnimation = () => {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(number => {
            const target = parseInt(number.getAttribute('data-target'));
            const countUp = new CountUp(number, target, {
                duration: 2.5,
                separator: ',',
                useGrouping: true
            });
            
            if (!countUp.error) {
                countUp.start();
            }
        });
    };

    window.addEventListener('scroll', showStatsOnScroll);
};

// Initialize stats animation when document is ready
document.addEventListener('DOMContentLoaded', () => {
    initStatsAnimation();
});

// Utility Functions

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Show Loading
function showLoading(element) {
    element.classList.add('loading');
}

// Hide Loading
function hideLoading(element) {
    element.classList.remove('loading');
}

// Show Toast Message
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // Add to page
    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Hide toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Error Handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // You can add error reporting here
});

// Performance Monitoring
window.addEventListener('load', function() {
    // Monitor page load time for optimization
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    // Store load time for analytics if needed
    window.pageLoadTime = loadTime;
});

// Add loading indicator to search
function addSearchLoading() {
    const searchBtn = document.getElementById('search-btn');
    if (searchBtn) {
        const originalHTML = searchBtn.innerHTML;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        searchBtn.disabled = true;

        setTimeout(() => {
            searchBtn.innerHTML = originalHTML;
            searchBtn.disabled = false;
        }, 2000);
    }
}

// Update perform search to include loading
function performSearch() {
    const searchInput = document.getElementById('search-input');
    const query = searchInput.value.trim();

    if (query) {
        // Add loading state
        addSearchLoading();

        // Simulate search (replace with actual search implementation)
        setTimeout(() => {
            showToast(`جستجو برای: ${query}`, 'success');
        }, 2000);
    }
}

// Export functions for use in other files
window.SerajApp = {
    changeLanguage,
    showToast,
    showLoading,
    hideLoading,
    formatNumber,
    showContent
};

// Parallax and Mouse Move Effects
document.addEventListener('mousemove', function(e) {
    const bg = document.querySelector('.hero-background');
    if (!bg) return;
    const x = e.clientX / window.innerWidth * 100;
    const y = e.clientY / window.innerHeight * 100;
    bg.style.background = `
        radial-gradient(circle at ${x+20}% ${y+10}%, rgba(255,255,255,0.13) 0, transparent 60%),
        radial-gradient(circle at ${x-30}% ${y+40}%, rgba(255,255,255,0.10) 0, transparent 70%)
    `;
});

// Top Rankings Interactive Functions
function showResearcherDetails(researcherId) {
    const researchers = {
        'li-wei': {
            name: 'Li Wei',
            field: 'Computer Science',
            institution: 'MIT',
            papers: 2847,
            citations: 45672,
            hIndex: 89,
            bio: 'Leading researcher in artificial intelligence and machine learning.'
        },
        'john-smith': {
            name: 'John Smith',
            field: 'Physics',
            institution: 'Stanford',
            papers: 2156,
            citations: 38945,
            hIndex: 76,
            bio: 'Renowned physicist specializing in quantum mechanics and particle physics.'
        },
        'zhang-ming': {
            name: 'Zhang Ming',
            field: 'Engineering',
            institution: 'Tsinghua',
            papers: 1923,
            citations: 32156,
            hIndex: 68,
            bio: 'Expert in materials engineering and nanotechnology applications.'
        },
        'sarah-johnson': {
            name: 'Sarah Johnson',
            field: 'Chemistry',
            institution: 'Harvard',
            papers: 1784,
            citations: 29834,
            hIndex: 62,
            bio: 'Pioneering research in organic chemistry and drug discovery.'
        }
    };

    const researcher = researchers[researcherId];
    if (researcher) {
        const message = `${researcher.name}\n${researcher.field} - ${researcher.institution}\n${researcher.papers} papers, ${researcher.citations} citations\nH-index: ${researcher.hIndex}\n\n${researcher.bio}`;
        showToast(message, 'info');
    }
}

function showInstitutionDetails(institutionId) {
    const institutions = {
        'mit': {
            name: 'Massachusetts Institute of Technology',
            location: 'Cambridge, MA, USA',
            papers: 45672,
            researchers: 3245,
            founded: 1861,
            description: 'Leading technological university with cutting-edge research.'
        },
        'stanford': {
            name: 'Stanford University',
            location: 'Stanford, CA, USA',
            papers: 42156,
            researchers: 2987,
            founded: 1885,
            description: 'Premier research university in Silicon Valley.'
        },
        'tsinghua': {
            name: 'Tsinghua University',
            location: 'Beijing, China',
            papers: 38923,
            researchers: 3456,
            founded: 1911,
            description: 'Top engineering and technology university in China.'
        },
        'harvard': {
            name: 'Harvard University',
            location: 'Cambridge, MA, USA',
            papers: 36784,
            researchers: 2876,
            founded: 1636,
            description: 'Oldest higher education institution in the United States.'
        }
    };

    const institution = institutions[institutionId];
    if (institution) {
        const message = `${institution.name}\n${institution.location}\nFounded: ${institution.founded}\n${institution.papers} papers, ${institution.researchers} researchers\n\n${institution.description}`;
        showToast(message, 'info');
    }
}

function showCountryDetails(countryId) {
    const countries = {
        'china': {
            name: 'China',
            population: '1.4B',
            papers: '28,456,789',
            universities: 2956,
            researchBudget: '$554B',
            description: 'Leading in scientific publications and research output.'
        },
        'usa': {
            name: 'United States',
            population: '331M',
            papers: '24,123,456',
            universities: 4298,
            researchBudget: '$656B',
            description: 'Global leader in research and innovation.'
        },
        'germany': {
            name: 'Germany',
            population: '83M',
            papers: '8,923,145',
            universities: 426,
            researchBudget: '$118B',
            description: 'European powerhouse in engineering and technology research.'
        },
        'uk': {
            name: 'United Kingdom',
            population: '67M',
            papers: '7,784,632',
            universities: 164,
            researchBudget: '$53B',
            description: 'Historic center of scientific advancement and discovery.'
        }
    };

    const country = countries[countryId];
    if (country) {
        const message = `${country.name}\nPopulation: ${country.population}\n${country.papers} papers\n${country.universities} universities\nR&D Budget: ${country.researchBudget}\n\n${country.description}`;
        showToast(message, 'info');
    }
}

// Services and Support Functions
function showServiceDetails(serviceId) {
    const services = {
        'advanced-search': {
            title: 'جستجوی پیشرفته',
            description: 'جستجوی دقیق و پیشرفته در میلیون‌ها مدرک علمی با فیلترهای تخصصی',
            features: ['فیلتر بر اساس سال انتشار', 'جستجو در متن کامل', 'فیلتر نویسنده و موسسه', 'جستجوی بولین']
        },
        'download-articles': {
            title: 'دانلود مقالات',
            description: 'دانلود مقالات و منابع علمی با کیفیت بالا',
            features: ['دانلود PDF', 'فرمت‌های مختلف', 'دسترسی آفلاین', 'مدیریت کتابخانه شخصی']
        },
        'citation-analysis': {
            title: 'تحلیل استنادی',
            description: 'تحلیل و بررسی الگوهای استنادی و تأثیرگذاری تحقیقات',
            features: ['نمودار شبکه استناد', 'محاسبه h-index', 'تحلیل همکاری', 'ردیابی تأثیر']
        },
        'research-tracking': {
            title: 'پیگیری تحقیقات',
            description: 'پیگیری آخرین تحقیقات و مقالات در حوزه‌های مورد علاقه',
            features: ['هشدار ایمیلی', 'RSS فید', 'پیگیری نویسندگان', 'موضوعات داغ']
        },
        'api-access': {
            title: 'دسترسی API',
            description: 'دسترسی برنامه‌نویسی به پایگاه داده سراج',
            features: ['RESTful API', 'مستندات کامل', 'نمونه کد', 'پشتیبانی فنی']
        },
        'bulk-download': {
            title: 'دانلود انبوه',
            description: 'دانلود تعداد زیادی مقاله به صورت یکجا',
            features: ['دانلود دسته‌ای', 'فرمت‌بندی خودکار', 'فشرده‌سازی', 'برنامه‌ریزی دانلود']
        }
    };

    const service = services[serviceId];
    if (service) {
        const featuresText = service.features.map(f => `• ${f}`).join('\n');
        const message = `${service.title}\n\n${service.description}\n\nویژگی‌ها:\n${featuresText}`;
        showToast(message, 'info');
    }
}

function showSupportPage(pageId) {
    const pages = {
        'user-guide': 'راهنمای کامل استفاده از سراج در حال آماده‌سازی است.',
        'faq': 'صفحه سوالات متداول به زودی راه‌اندازی می‌شود.',
        'report-issue': 'سیستم گزارش مشکل در حال توسعه است.',
        'tutorials': 'آموزش‌های ویدیویی به زودی منتشر می‌شوند.',
        'webinars': 'وبینارهای آموزشی ماهانه برگزار می‌شود.'
    };

    const message = pages[pageId] || 'این صفحه در حال توسعه است.';
    showToast(message, 'info');
}

function showContactModal() {
    const modal = new bootstrap.Modal(document.getElementById('contactModal'));
    modal.show();

    // Handle form submission
    const form = document.getElementById('contactForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const name = document.getElementById('contactName').value;
        const email = document.getElementById('contactEmail').value;
        const subject = document.getElementById('contactSubject').value;
        const message = document.getElementById('contactMessage').value;

        // Simulate form submission
        showToast('پیام شما با موفقیت ارسال شد. به زودی پاسخ خواهیم داد.', 'success');
        modal.hide();
        form.reset();
    });
}

function showLegalPage(pageId) {
    const legalContent = {
        'privacy': {
            title: 'سیاست حریم خصوصی',
            content: `
                <h6>جمع‌آوری اطلاعات</h6>
                <p>ما اطلاعات شما را تنها برای بهبود خدمات جمع‌آوری می‌کنیم.</p>

                <h6>استفاده از اطلاعات</h6>
                <p>اطلاعات شما هرگز با اشخاص ثالث به اشتراک گذاشته نمی‌شود.</p>

                <h6>امنیت داده‌ها</h6>
                <p>ما از بالاترین استانداردهای امنیتی برای محافظت از داده‌های شما استفاده می‌کنیم.</p>

                <h6>حقوق کاربران</h6>
                <p>شما حق دسترسی، تصحیح و حذف اطلاعات شخصی خود را دارید.</p>
            `
        },
        'terms': {
            title: 'شرایط استفاده',
            content: `
                <h6>پذیرش شرایط</h6>
                <p>با استفاده از سراج، شما این شرایط را می‌پذیرید.</p>

                <h6>استفاده مجاز</h6>
                <p>استفاده از سراج باید مطابق با قوانین و مقررات باشد.</p>

                <h6>حقوق مالکیت معنوی</h6>
                <p>تمامی محتوای سراج تحت حمایت قوانین مالکیت معنوی است.</p>

                <h6>محدودیت‌ها</h6>
                <p>استفاده تجاری بدون مجوز ممنوع است.</p>
            `
        },
        'cookies': {
            title: 'سیاست کوکی‌ها',
            content: `
                <h6>کوکی‌ها چیست؟</h6>
                <p>کوکی‌ها فایل‌های کوچکی هستند که برای بهبود تجربه کاربری استفاده می‌شوند.</p>

                <h6>انواع کوکی‌ها</h6>
                <p>ما از کوکی‌های ضروری، عملکردی و تحلیلی استفاده می‌کنیم.</p>

                <h6>مدیریت کوکی‌ها</h6>
                <p>شما می‌توانید کوکی‌ها را از طریق تنظیمات مرورگر مدیریت کنید.</p>
            `
        },
        'accessibility': {
            title: 'دسترسی‌پذیری',
            content: `
                <h6>تعهد ما</h6>
                <p>سراج متعهد به فراهم کردن دسترسی برابر برای همه کاربران است.</p>

                <h6>ویژگی‌های دسترسی‌پذیری</h6>
                <p>• پشتیبانی از صفحه‌خوان‌ها<br>
                • ناوبری با کیبورد<br>
                • کنتراست بالا<br>
                • متن قابل تغییر اندازه</p>

                <h6>بازخورد</h6>
                <p>اگر مشکلی در دسترسی‌پذیری مشاهده کردید، لطفاً با ما تماس بگیرید.</p>
            `
        }
    };

    const page = legalContent[pageId];
    if (page) {
        document.getElementById('legalModalLabel').textContent = page.title;
        document.getElementById('legalContent').innerHTML = page.content;

        const modal = new bootstrap.Modal(document.getElementById('legalModal'));
        modal.show();
    }
}

// Make functions globally available
window.showResearcherDetails = showResearcherDetails;
window.showInstitutionDetails = showInstitutionDetails;
window.showCountryDetails = showCountryDetails;
window.showServiceDetails = showServiceDetails;
window.showSupportPage = showSupportPage;
window.showContactModal = showContactModal;
window.showLegalPage = showLegalPage;
