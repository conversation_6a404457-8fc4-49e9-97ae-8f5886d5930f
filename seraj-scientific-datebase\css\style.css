/* Seraj Scientific Database - Main Styles */

/* Import Google Fonts for Persian and English */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1f2937;
    --text-color: #374151;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Vazirmatn', 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #ffffff;
    overflow-x: hidden;
}

html {
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-color);
    opacity: 0.8;
}

/* Navigation */
.navbar {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95) !important;
    transition: var(--transition);
    padding: 1rem 0;
    display: flex;
    align-items: center;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.navbar-brand {
    font-size: 2rem !important;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.navbar-nav-container {
    display: flex;
    align-items: center;
}

.navbar-nav {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0;
    padding: 0;
    list-style: none;
}

/* Enhanced Language Switcher */
.language-switcher {
    position: relative;
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(30, 58, 138, 0.2);
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.language-toggle:hover {
    background: rgba(30, 58, 138, 0.1);
    color: var(--primary-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.language-icon {
    font-size: 1rem;
    color: var(--secondary-color);
}

.language-text {
    font-size: 0.9rem;
    font-weight: 500;
}

.dropdown-arrow {
    font-size: 0.7rem;
    transition: transform 0.3s ease;
}

.language-toggle[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

.language-menu {
    min-width: 200px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
}

.language-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    border: none;
    background: none;
}

.language-option:hover {
    background: rgba(30, 58, 138, 0.05);
    color: var(--primary-color);
    transform: translateX(2px);
}

.language-option:active {
    background: rgba(30, 58, 138, 0.1);
}

.language-option span {
    font-weight: 500;
    flex: 1;
}

.language-option small {
    font-size: 0.75rem;
    opacity: 0.7;
}

.flag-iran {
    color: #239f40;
}

.flag-us {
    color: #1e40af;
}

/* Enhanced Top Rankings Sections */
.top-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.top-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.top-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.top-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.top-card:hover::before {
    transform: scaleX(1);
}

.top-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 1);
}

.top-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
    transition: all 0.3s ease;
}

.top-card:hover .top-avatar {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(30, 58, 138, 0.4);
}

.top-card h5 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.top-card:hover h5 {
    color: var(--secondary-color);
}

.top-card .text-muted {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.top-card .badge {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.top-card:hover .badge {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Ranking indicators */
.ranking-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--accent-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: 0 2px 10px rgba(245, 158, 11, 0.3);
}

.ranking-indicator.rank-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #92400e;
}

.ranking-indicator.rank-2 {
    background: linear-gradient(135deg, #c0c0c0, #e5e7eb);
    color: #374151;
}

.ranking-indicator.rank-3 {
    background: linear-gradient(135deg, #cd7f32, #d97706);
    color: white;
}

/* Interactive elements */
.top-card-actions {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.top-card:hover .top-card-actions {
    opacity: 1;
    transform: translateY(0);
}

.action-btn {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: 8px;
    background: rgba(30, 58, 138, 0.1);
    color: var(--primary-color);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Enhanced Footer Styles */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footerGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="%23374151" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23footerGrid)"/></svg>');
    opacity: 0.1;
    pointer-events: none;
}

.footer-title {
    color: #f9fafb;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 1px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-link {
    color: #9ca3af;
    text-decoration: none;
    display: block;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 1rem;
}

.footer-link::before {
    content: '→';
    position: absolute;
    left: 0;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    color: var(--secondary-color);
}

.footer-link:hover {
    color: #f9fafb;
    text-decoration: none;
    transform: translateX(5px);
}

.footer-link:hover::before {
    opacity: 1;
    transform: translateX(0);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.1);
    color: var(--secondary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.social-link:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.contact-info {
    list-style: none;
    padding: 0;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-icon {
    color: var(--secondary-color);
    font-size: 1.2rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.contact-link {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-link:hover {
    color: #f9fafb;
    text-decoration: underline;
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.footer-legal-links {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.footer-legal-link {
    color: #9ca3af;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
    position: relative;
}

.footer-legal-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--secondary-color);
    transition: width 0.3s ease;
}

.footer-legal-link:hover {
    color: #f9fafb;
    text-decoration: none;
}

.footer-legal-link:hover::after {
    width: 100%;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 16px 16px 0 0;
    border: none;
}

.modal-title {
    font-weight: 600;
}

.btn-close {
    filter: invert(1);
}

.contact-details {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
}

.contact-detail-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.contact-detail-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.contact-detail-item i {
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.contact-detail-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-detail-item a:hover {
    text-decoration: underline;
}
}

.navbar-nav .nav-item {
    margin-left: 1rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.navbar-nav .nav-link:hover {
    background-color: var(--light-color);
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link i {
    margin-left: 0.5rem;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    transform: none !important;
    margin-top: 0.5rem !important;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
    background-color: white;
    z-index: 1050;
    min-width: 120px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
    color: var(--text-color);
    text-decoration: none;
    display: block;
    white-space: nowrap;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.dropdown-item:focus {
    background-color: var(--light-color);
    color: var(--primary-color);
    outline: none;
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    background: linear-gradient(120deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.hero-background {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    width: 100%; height: 100%;
    z-index: 0;
    pointer-events: none;
    background: radial-gradient(circle at 60% 40%, rgba(245, 158, 11, 0.10) 0, transparent 60%),
                radial-gradient(circle at 30% 70%, rgba(6, 182, 212, 0.13) 0, transparent 70%);
    transition: background 0.7s cubic-bezier(0.4,0,0.2,1);
    animation: gradientMove 8s ease-in-out infinite alternate;
}

@keyframes gradientMove {
    0% { background-position: 60% 40%, 30% 70%; }
    100% { background-position: 70% 50%, 20% 80%; }
}

.hero-section:hover .hero-background {
    background: radial-gradient(circle at 70% 50%, rgba(16,185,129,0.18) 0, transparent 60%),
                radial-gradient(circle at 20% 80%, rgba(59,130,246,0.18) 0, transparent 70%);
    filter: brightness(1.08) saturate(1.2);
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 1rem;
    background: rgba(255,255,255,0.10);
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(80,80,160,0.08);
    backdrop-filter: blur(2px);
}

.hero-title {
    color: #fff;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-shadow: 0 2px 8px rgba(80,80,160,0.12);
}

/* Search Container - Primary Focus */
.search-container {
    background: rgba(255,255,255,0.95);
    border-radius: 50px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    padding: 0.5rem;
    margin: 0 auto;
    max-width: 500px;
    transition: box-shadow 0.3s;
}

.search-container:hover {
    box-shadow: 0 8px 32px rgba(80,80,160,0.16);
}

.search-wrapper {
    display: flex;
    align-items: center;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    outline: none;
}

.search-btn {
    background: #4f46e5;
    color: #fff;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    transition: background 0.3s;
}

.search-btn:hover {
    background: #6366f1;
}

/* Statistics Cards */
.hero-stats {
    margin-top: 2rem;
}

.stats-section {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
    background-color: #f8f9fa;
}

.stats-section.visible {
    opacity: 1;
    transform: translateY(0);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    margin-bottom: 0;
}

/* Tutorial Section */
.tutorial-section {
    padding: 80px 0;
}

.tutorial-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.tutorial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.tutorial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.tutorial-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.tutorial-step {
    color: var(--accent-color);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tutorial-title {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.tutorial-desc {
    color: var(--text-color);
    opacity: 0.8;
    line-height: 1.6;
}

/* Top Section */
.top-section {
    padding: 80px 0;
    background: white;
}

.top-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    height: 100%;
}

.top-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.top-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.top-card h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.top-card .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* Statistics Section */
.statistics-section {
    padding: 80px 0;
}

.chart-container {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    height: 400px;
}

.chart-container h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 2rem;
}

/* Footer */
.footer {
    background: var(--dark-color) !important;
    color: #d1d5db;
    padding: 4rem 0 2rem;
}

.footer h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.footer .text-muted {
    color: #9ca3af !important;
    line-height: 1.8;
    font-size: 0.95rem;
}

.footer a {
    transition: var(--transition);
    display: block;
    padding: 0.5rem 0;
}

.footer a:hover {
    color: var(--secondary-color) !important;
    transform: translateX(5px);
}

.footer ul li {
    margin-bottom: 0.5rem;
}

.footer i {
    width: 20px;
    text-align: center;
    margin-left: 0.5rem;
    color: var(--secondary-color);
}

.footer hr {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Loading Skeleton Styles */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.skeleton::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: skeleton-shimmer 2s infinite;
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.75rem;
}

.skeleton-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.skeleton-card {
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.skeleton-chart {
    height: 300px;
    border-radius: var(--border-radius);
}

.skeleton-stat {
    height: 80px;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes skeleton-shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Hide content while loading */
.loading-state .content {
    display: none;
}

.loading-state .skeleton {
    display: block;
}

.content-loaded .skeleton {
    display: none;
}

.content-loaded .content {
    display: block;
}

/* Loading Active State */
.loading-active {
    animation-duration: 1s !important;
}

.loading-active::after {
    animation-duration: 1.5s !important;
}

/* Toast Messages */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.toast.show {
    transform: translateX(0);
}

.toast.toast-success {
    background-color: var(--success-color);
}

.toast.toast-error {
    background-color: var(--danger-color);
}

.toast.toast-warning {
    background-color: var(--warning-color);
}

.toast.toast-info {
    background-color: var(--info-color);
}

/* Loading Spinner */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
