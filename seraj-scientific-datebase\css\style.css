/* Seraj Scientific Database - Main Styles */

/* Import Google Fonts for Persian and English */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1f2937;
    --text-color: #374151;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Vazirmatn', 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #ffffff;
    overflow-x: hidden;
}

html {
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-color);
    opacity: 0.8;
}

/* Navigation */
.navbar {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95) !important;
    transition: var(--transition);
    padding: 1rem 0;
    display: flex;
    align-items: center;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.navbar-brand {
    font-size: 2rem !important;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.navbar-nav-container {
    display: flex;
    align-items: center;
}

.navbar-nav {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0;
    padding: 0;
    list-style: none;
}

.navbar-nav .nav-item {
    margin-left: 1rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.navbar-nav .nav-link:hover {
    background-color: var(--light-color);
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link i {
    margin-left: 0.5rem;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    transform: none !important;
    margin-top: 0.5rem !important;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
    background-color: white;
    z-index: 1050;
    min-width: 120px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
    color: var(--text-color);
    text-decoration: none;
    display: block;
    white-space: nowrap;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.dropdown-item:focus {
    background-color: var(--light-color);
    color: var(--primary-color);
    outline: none;
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    background: linear-gradient(120deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.hero-background {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    width: 100%; height: 100%;
    z-index: 0;
    pointer-events: none;
    background: radial-gradient(circle at 60% 40%, rgba(245, 158, 11, 0.10) 0, transparent 60%),
                radial-gradient(circle at 30% 70%, rgba(6, 182, 212, 0.13) 0, transparent 70%);
    transition: background 0.7s cubic-bezier(0.4,0,0.2,1);
    animation: gradientMove 8s ease-in-out infinite alternate;
}

@keyframes gradientMove {
    0% { background-position: 60% 40%, 30% 70%; }
    100% { background-position: 70% 50%, 20% 80%; }
}

.hero-section:hover .hero-background {
    background: radial-gradient(circle at 70% 50%, rgba(16,185,129,0.18) 0, transparent 60%),
                radial-gradient(circle at 20% 80%, rgba(59,130,246,0.18) 0, transparent 70%);
    filter: brightness(1.08) saturate(1.2);
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 1rem;
    background: rgba(255,255,255,0.10);
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(80,80,160,0.08);
    backdrop-filter: blur(2px);
}

.hero-title {
    color: #fff;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-shadow: 0 2px 8px rgba(80,80,160,0.12);
}

/* Search Container - Primary Focus */
.search-container {
    background: rgba(255,255,255,0.95);
    border-radius: 50px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    padding: 0.5rem;
    margin: 0 auto;
    max-width: 500px;
    transition: box-shadow 0.3s;
}

.search-container:hover {
    box-shadow: 0 8px 32px rgba(80,80,160,0.16);
}

.search-wrapper {
    display: flex;
    align-items: center;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    outline: none;
}

.search-btn {
    background: #4f46e5;
    color: #fff;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    transition: background 0.3s;
}

.search-btn:hover {
    background: #6366f1;
}

/* Statistics Cards */
.hero-stats {
    margin-top: 2rem;
}

.stats-section {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
    background-color: #f8f9fa;
}

.stats-section.visible {
    opacity: 1;
    transform: translateY(0);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    margin-bottom: 0;
}

/* Tutorial Section */
.tutorial-section {
    padding: 80px 0;
}

.tutorial-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.tutorial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.tutorial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.tutorial-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.tutorial-step {
    color: var(--accent-color);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tutorial-title {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.tutorial-desc {
    color: var(--text-color);
    opacity: 0.8;
    line-height: 1.6;
}

/* Top Section */
.top-section {
    padding: 80px 0;
    background: white;
}

.top-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    height: 100%;
}

.top-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.top-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.top-card h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.top-card .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* Statistics Section */
.statistics-section {
    padding: 80px 0;
}

.chart-container {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    height: 400px;
}

.chart-container h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 2rem;
}

/* Footer */
.footer {
    background: var(--dark-color) !important;
    color: #d1d5db;
    padding: 4rem 0 2rem;
}

.footer h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.footer .text-muted {
    color: #9ca3af !important;
    line-height: 1.8;
    font-size: 0.95rem;
}

.footer a {
    transition: var(--transition);
    display: block;
    padding: 0.5rem 0;
}

.footer a:hover {
    color: var(--secondary-color) !important;
    transform: translateX(5px);
}

.footer ul li {
    margin-bottom: 0.5rem;
}

.footer i {
    width: 20px;
    text-align: center;
    margin-left: 0.5rem;
    color: var(--secondary-color);
}

.footer hr {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Loading Skeleton Styles */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.skeleton::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: skeleton-shimmer 2s infinite;
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.75rem;
}

.skeleton-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.skeleton-card {
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.skeleton-chart {
    height: 300px;
    border-radius: var(--border-radius);
}

.skeleton-stat {
    height: 80px;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes skeleton-shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Hide content while loading */
.loading-state .content {
    display: none;
}

.loading-state .skeleton {
    display: block;
}

.content-loaded .skeleton {
    display: none;
}

.content-loaded .content {
    display: block;
}

/* Loading Active State */
.loading-active {
    animation-duration: 1s !important;
}

.loading-active::after {
    animation-duration: 1.5s !important;
}

/* Toast Messages */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.toast.show {
    transform: translateX(0);
}

.toast.toast-success {
    background-color: var(--success-color);
}

.toast.toast-error {
    background-color: var(--danger-color);
}

.toast.toast-warning {
    background-color: var(--warning-color);
}

.toast.toast-info {
    background-color: var(--info-color);
}

/* Loading Spinner */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
