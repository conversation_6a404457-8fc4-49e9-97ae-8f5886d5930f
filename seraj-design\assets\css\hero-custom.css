/* Custom Seraj Scientific Database Styles */

/* Header Styles */
.seraj-header {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(196, 238, 24, 0.1);
    transition: all 0.3s ease;
}

.seraj-header.header-sticky {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Logo Styles */
.seraj-logo a {
    text-decoration: none;
    transition: all 0.3s ease;
}

.seraj-logo a:hover {
    transform: translateY(-2px);
}

.seraj-logo-icon svg {
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 8px rgba(196, 238, 24, 0.3));
}

.seraj-logo a:hover .seraj-logo-icon svg {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 12px rgba(196, 238, 24, 0.4));
}

.seraj-logo-text h1 {
    background: linear-gradient(135deg, #030303, #405955);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation Styles */
.seraj-nav ul {
    list-style: none;
    padding: 0;
}

.tp-nav-link {
    position: relative;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: inline-block;
}

.tp-nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #C4EE18, #10302a);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.tp-nav-link:hover::before,
.tp-nav-link.active::before {
    width: 80%;
}

.tp-nav-link:hover {
    background: rgba(196, 238, 24, 0.1);
    color: #10302a !important;
    transform: translateY(-1px);
}

/* Header Action Buttons */
.seraj-search-btn {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    border: 1px solid rgba(196, 238, 24, 0.2);
    background: rgba(255, 255, 255, 0.8);
    color: #405955;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.seraj-search-btn:hover {
    background: #C4EE18;
    color: #030303;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(196, 238, 24, 0.3);
}

/* Language Dropdown */
.seraj-lang-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 12px;
    border: 1px solid rgba(196, 238, 24, 0.2);
    background: rgba(255, 255, 255, 0.8);
    color: #405955;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.seraj-lang-btn:hover {
    background: rgba(196, 238, 24, 0.1);
    border-color: rgba(196, 238, 24, 0.4);
    transform: translateY(-1px);
}

.seraj-lang-menu {
    border-radius: 12px;
    border: 1px solid rgba(196, 238, 24, 0.2);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 8px;
    margin-top: 8px;
}

.seraj-lang-menu .dropdown-item {
    border-radius: 8px;
    padding: 8px 12px;
    transition: all 0.2s ease;
    color: #405955;
}

.seraj-lang-menu .dropdown-item:hover {
    background: rgba(196, 238, 24, 0.1);
    color: #10302a;
}

/* Login Button */
.seraj-login-btn {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, #C4EE18, #10302a);
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(196, 238, 24, 0.2);
}

.seraj-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(196, 238, 24, 0.3);
    color: #fff;
    text-decoration: none;
}

/* Mobile Menu Button */
.seraj-mobile-menu-btn {
    width: 44px;
    height: 44px;
    border: none;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(196, 238, 24, 0.2);
}

.seraj-mobile-menu-btn span {
    width: 20px;
    height: 2px;
    background: #405955;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.seraj-mobile-menu-btn:hover {
    background: rgba(196, 238, 24, 0.1);
    transform: translateY(-1px);
}

.seraj-mobile-menu-btn:hover span {
    background: #10302a;
}

/* Hero Section Styles */
.seraj-hero {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.seraj-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(196, 238, 24, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(16, 48, 42, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* Hero Title */
.seraj-hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    line-height: 1.2;
    margin-bottom: 2rem;
}

.seraj-hero-title-main {
    display: block;
    color: #030303;
    font-weight: 800;
}

.seraj-hero-title-highlight {
    display: block;
    background: linear-gradient(135deg, #C4EE18, #10302a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 900;
    position: relative;
}

.seraj-hero-title-highlight::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #C4EE18, #10302a);
    border-radius: 2px;
    opacity: 0.6;
}

/* Hero Subtitle */
.seraj-hero-subtitle {
    max-width: 600px;
    margin: 0 auto;
    color: #64748b;
}

/* Statistics Section */
.seraj-hero-stats {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
}

.seraj-stat-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 0 20px;
}

.seraj-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(196, 238, 24, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.seraj-stat-content h3 {
    line-height: 1;
    margin-bottom: 4px;
}

.seraj-stat-divider {
    width: 1px;
    height: 40px;
    background: linear-gradient(to bottom, transparent, rgba(196, 238, 24, 0.3), transparent);
}

/* AI Search Section */
.seraj-ai-search-section {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 24px;
    padding: 40px;
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.seraj-ai-search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #C4EE18, transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

/* AI Badge */
.seraj-ai-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: linear-gradient(135deg, #C4EE18, #10302a);
    color: white;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 4px 16px rgba(196, 238, 24, 0.3);
}

/* Search Input */
.seraj-search-input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.seraj-ai-search-input {
    width: 100%;
    padding: 20px 60px 20px 24px;
    border: 2px solid rgba(196, 238, 24, 0.3);
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
    color: #1e293b;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.seraj-ai-search-input:focus {
    outline: none;
    border-color: #C4EE18;
    box-shadow: 0 0 0 4px rgba(196, 238, 24, 0.1), 0 8px 32px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 1);
}

.seraj-ai-search-input::placeholder {
    color: #94a3b8;
}

/* Search Button */
.seraj-search-btn-ai {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #C4EE18, #10302a);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(196, 238, 24, 0.3);
}

.seraj-search-btn-ai:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 8px 24px rgba(196, 238, 24, 0.4);
}

/* Search Suggestions */
.seraj-search-suggestions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

.seraj-suggestion-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(196, 238, 24, 0.1);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 20px;
    color: #475569;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.seraj-suggestion-item:hover {
    background: rgba(196, 238, 24, 0.2);
    border-color: rgba(196, 238, 24, 0.4);
    transform: translateY(-1px);
}

.seraj-suggestion-item svg {
    color: #10302a;
    flex-shrink: 0;
}

/* Statistics Dashboard Section */
.seraj-statistics-area {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
}

.seraj-statistics-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 10% 20%, rgba(196, 238, 24, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(16, 48, 42, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

/* Section Header */
.seraj-section-header {
    position: relative;
    z-index: 2;
}

.seraj-section-subtitle {
    display: inline-flex;
    align-items: center;
    padding: 8px 20px;
    background: rgba(196, 238, 24, 0.1);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 20px;
    color: #10302a;
}

.seraj-section-title {
    line-height: 1.2;
    background: linear-gradient(135deg, #030303, #405955);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Chart Cards */
.seraj-chart-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    height: 100%;
}

.seraj-chart-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.1);
    border-color: rgba(196, 238, 24, 0.4);
}

.seraj-chart-container {
    position: relative;
    height: 300px;
}

.seraj-chart-container canvas {
    max-width: 100%;
    height: auto !important;
}

/* Ranking Cards */
.seraj-ranking-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    height: 100%;
}

.seraj-ranking-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.1);
    border-color: rgba(196, 238, 24, 0.4);
}

.seraj-ranking-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(196, 238, 24, 0.2);
}

.seraj-ranking-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: rgba(196, 238, 24, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.seraj-ranking-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.seraj-ranking-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 12px;
    transition: all 0.2s ease;
}

.seraj-ranking-item:hover {
    background: rgba(196, 238, 24, 0.05);
    transform: translateX(4px);
}

.seraj-ranking-position {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: linear-gradient(135deg, #C4EE18, #10302a);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    flex-shrink: 0;
}

.seraj-ranking-info {
    flex: 1;
    min-width: 0;
}

.seraj-ranking-info h5 {
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.seraj-ranking-progress {
    width: 60px;
    height: 6px;
    background: rgba(196, 238, 24, 0.2);
    border-radius: 3px;
    overflow: hidden;
    flex-shrink: 0;
}

.seraj-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #C4EE18, #10302a);
    border-radius: 3px;
    transition: width 0.8s ease;
}

/* How to Use Section */
.seraj-howto-area {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    overflow: hidden;
}

.seraj-howto-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(196, 238, 24, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(16, 48, 42, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

/* Step Cards */
.seraj-step-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 24px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.seraj-step-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, #C4EE18, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.seraj-step-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border-color: rgba(196, 238, 24, 0.4);
}

.seraj-step-card:hover::before {
    opacity: 1;
}

/* Step Number */
.seraj-step-number {
    position: absolute;
    top: -15px;
    right: 30px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #C4EE18, #10302a);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 18px;
    box-shadow: 0 4px 16px rgba(196, 238, 24, 0.3);
    z-index: 2;
}

/* Step Icon */
.seraj-step-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 30px;
    border-radius: 20px;
    background: rgba(196, 238, 24, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #10302a;
    transition: all 0.3s ease;
}

.seraj-step-card:hover .seraj-step-icon {
    background: rgba(196, 238, 24, 0.2);
    transform: scale(1.05);
}

/* Step Content */
.seraj-step-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.seraj-step-content h4 {
    margin-bottom: 15px;
}

.seraj-step-content p {
    flex: 1;
    margin-bottom: 20px;
}

/* Step Features */
.seraj-step-features {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: auto;
}

.seraj-feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #64748b;
    font-size: 14px;
}

.seraj-feature-item svg {
    flex-shrink: 0;
}

/* Call to Action Box */
.seraj-cta-box {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 24px;
    padding: 50px 40px;
    position: relative;
    overflow: hidden;
}

.seraj-cta-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #C4EE18, #10302a, #C4EE18);
    animation: shimmer 3s ease-in-out infinite;
}

/* CTA Button */
.seraj-cta-btn {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    background: linear-gradient(135deg, #C4EE18, #10302a);
    color: white;
    text-decoration: none;
    border-radius: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(196, 238, 24, 0.3);
}

.seraj-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(196, 238, 24, 0.4);
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .seraj-step-card {
        padding: 30px 20px;
        margin-bottom: 30px;
    }

    .seraj-step-number {
        right: 20px;
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .seraj-step-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
    }

    .seraj-cta-box {
        padding: 40px 30px;
    }
}

/* About Section */
.seraj-about-area {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;
}

.seraj-about-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(196, 238, 24, 0.04) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(16, 48, 42, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

/* About Content */
.seraj-about-content {
    position: relative;
    z-index: 2;
}

.seraj-about-mission h3 {
    position: relative;
}

.seraj-about-mission h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #C4EE18, #10302a);
    border-radius: 2px;
}

/* Feature Items */
.seraj-features-list {
    margin-top: 30px;
}

.seraj-feature-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(196, 238, 24, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.seraj-feature-item:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(196, 238, 24, 0.3);
    transform: translateX(8px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
}

.seraj-feature-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(196, 238, 24, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.seraj-feature-item:hover .seraj-feature-icon {
    background: rgba(196, 238, 24, 0.2);
    transform: scale(1.05);
}

.seraj-feature-content {
    flex: 1;
}

/* Visual Section */
.seraj-about-visual {
    position: relative;
    z-index: 2;
}

.seraj-visual-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 24px;
    padding: 40px;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.seraj-visual-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #C4EE18, #10302a, #C4EE18);
    animation: shimmer 3s ease-in-out infinite;
}

/* Stat Boxes */
.seraj-stat-box {
    text-align: center;
    padding: 30px 20px;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 16px;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.seraj-stat-box:hover {
    background: rgba(196, 238, 24, 0.05);
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
}

.seraj-stat-box .seraj-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.seraj-stat-box:hover .seraj-stat-icon {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 1);
}

.seraj-stat-box h4 {
    line-height: 1;
    margin-bottom: 8px;
}

/* Vision Box */
.seraj-vision-box {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 24px;
    padding: 50px 40px;
    margin-top: 40px;
    position: relative;
    overflow: hidden;
}

.seraj-vision-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, #C4EE18, transparent);
    opacity: 0.6;
}

.seraj-vision-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .seraj-feature-item {
        padding: 16px;
        margin-bottom: 16px;
    }

    .seraj-feature-icon {
        width: 40px;
        height: 40px;
    }

    .seraj-visual-card {
        padding: 30px 20px;
    }

    .seraj-stat-box {
        padding: 20px 15px;
        margin-bottom: 20px;
    }

    .seraj-vision-box {
        padding: 40px 30px;
        text-align: center;
    }

    .seraj-vision-icon {
        margin-top: 30px;
    }
}

/* Services Section */
.seraj-services-area {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    overflow: hidden;
}

.seraj-services-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 25%, rgba(196, 238, 24, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 85% 75%, rgba(16, 48, 42, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

/* Service Cards */
.seraj-service-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 24px;
    padding: 40px 30px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.seraj-service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, #C4EE18, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.seraj-service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border-color: rgba(196, 238, 24, 0.4);
}

.seraj-service-card:hover::before {
    opacity: 1;
}

/* Service Icon */
.seraj-service-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: rgba(196, 238, 24, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #10302a;
    margin-bottom: 30px;
    transition: all 0.3s ease;
    align-self: flex-start;
}

.seraj-service-card:hover .seraj-service-icon {
    background: rgba(196, 238, 24, 0.2);
    transform: scale(1.05);
}

/* Service Content */
.seraj-service-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.seraj-service-content h4 {
    margin-bottom: 15px;
    line-height: 1.3;
}

.seraj-service-content p {
    flex: 1;
    margin-bottom: 20px;
}

/* Service Features */
.seraj-service-features {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: auto;
}

.seraj-service-features li {
    position: relative;
    padding-left: 20px;
    color: #64748b;
    font-size: 14px;
}

.seraj-service-features li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: #C4EE18;
    border-radius: 50%;
}

/* Services CTA */
.seraj-services-cta {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(196, 238, 24, 0.2);
    border-radius: 24px;
    padding: 50px 40px;
    position: relative;
    overflow: hidden;
}

.seraj-services-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #C4EE18, #10302a, #C4EE18);
    animation: shimmer 3s ease-in-out infinite;
}

/* CTA Buttons */
.seraj-cta-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.seraj-cta-btn-primary {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    background: linear-gradient(135deg, #C4EE18, #10302a);
    color: white;
    text-decoration: none;
    border-radius: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(196, 238, 24, 0.3);
}

.seraj-cta-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(196, 238, 24, 0.4);
    color: white;
    text-decoration: none;
}

.seraj-cta-btn-secondary {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    background: rgba(255, 255, 255, 0.9);
    color: #10302a;
    text-decoration: none;
    border: 2px solid rgba(196, 238, 24, 0.3);
    border-radius: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.seraj-cta-btn-secondary:hover {
    background: rgba(196, 238, 24, 0.1);
    border-color: rgba(196, 238, 24, 0.5);
    transform: translateY(-2px);
    color: #10302a;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .seraj-service-card {
        padding: 30px 20px;
        margin-bottom: 30px;
    }

    .seraj-service-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
    }

    .seraj-services-cta {
        padding: 40px 30px;
    }

    .seraj-cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .seraj-cta-btn-primary,
    .seraj-cta-btn-secondary {
        width: 100%;
        justify-content: center;
        max-width: 280px;
    }
}

/* Footer Section */
.seraj-footer-area {
    position: relative;
    background: linear-gradient(135deg, #10302a 0%, #030303 100%);
    overflow: hidden;
}

.seraj-footer-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.seraj-footer-content {
    position: relative;
    z-index: 2;
}

/* Footer Widgets */
.seraj-footer-widget {
    margin-bottom: 40px;
}

.seraj-footer-logo {
    margin-bottom: 30px;
}

.seraj-logo-icon {
    margin-right: 15px;
}

.seraj-footer-title {
    color: white;
    margin-bottom: 30px;
    position: relative;
}

.seraj-footer-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: #C4EE18;
    border-radius: 1px;
}

/* Footer Links */
.seraj-footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.seraj-footer-links li {
    margin-bottom: 12px;
}

.seraj-footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
}

.seraj-footer-links a:hover {
    color: #C4EE18;
    transform: translateX(5px);
    text-decoration: none;
}

/* Social Links */
.seraj-social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.seraj-social-link {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.seraj-social-link:hover {
    background: #C4EE18;
    color: #10302a;
    transform: translateY(-3px);
    text-decoration: none;
}

/* Contact Info */
.seraj-contact-info {
    margin-top: 20px;
}

.seraj-contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.seraj-contact-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(196, 238, 24, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.seraj-contact-item a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.seraj-contact-item a:hover {
    color: #C4EE18;
    text-decoration: none;
}

/* Footer Bottom */
.seraj-footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 40px;
}

.seraj-copyright p {
    margin: 0;
    color: rgba(255, 255, 255, 0.5);
}

.seraj-legal-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 20px;
    justify-content: flex-end;
}

.seraj-legal-links li a {
    color: rgba(255, 255, 255, 0.5);
    text-decoration: none;
    transition: color 0.3s ease;
}

.seraj-legal-links li a:hover {
    color: #C4EE18;
    text-decoration: none;
}

/* Text Colors */
.text-white-70 {
    color: rgba(255, 255, 255, 0.7) !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .seraj-footer-widget {
        margin-bottom: 40px;
        text-align: center;
    }

    .seraj-social-links {
        justify-content: center;
    }

    .seraj-legal-links {
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .seraj-footer-bottom {
        text-align: center;
    }

    .seraj-footer-bottom .col-md-6:first-child {
        margin-bottom: 20px;
    }
}

/* Search Section Styling */
.tp-hero-search-section {
    margin-top: 60px;
    padding: 40px 20px;
    background: linear-gradient(135deg, rgba(196, 238, 24, 0.1), rgba(196, 238, 24, 0.05));
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(196, 238, 24, 0.2);
}

.tp-hero-search-input {
    background: #ffffff !important;
    border: 3px solid #C4EE18 !important;
    border-radius: 50px !important;
    padding: 25px 70px 25px 35px !important;
    font-size: 20px !important;
    font-family: 'Vazirmatn', sans-serif !important;
    color: #333 !important;
    box-shadow: 0 15px 40px rgba(196, 238, 24, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.4s ease !important;
    min-height: 80px !important;
    font-weight: 500 !important;
}

.tp-hero-search-input:focus {
    outline: none !important;
    border-color: #a8d916 !important;
    box-shadow: 0 0 50px rgba(196, 238, 24, 0.8), 0 20px 50px rgba(0, 0, 0, 0.2) !important;
    transform: translateY(-3px) !important;
    background: #fefffe !important;
}

.tp-hero-search-input::placeholder {
    color: #666 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
}

.tp-search-form-icon {
    right: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: linear-gradient(135deg, #C4EE18, #a8d916) !important;
    border: none !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.4s ease !important;
    box-shadow: 0 8px 25px rgba(196, 238, 24, 0.5), 0 0 20px rgba(196, 238, 24, 0.3) !important;
    z-index: 10 !important;
}

.tp-search-form-icon:hover {
    background: linear-gradient(135deg, #a8d916, #95c214) !important;
    box-shadow: 0 12px 35px rgba(196, 238, 24, 0.7), 0 0 30px rgba(196, 238, 24, 0.5) !important;
    transform: translateY(-50%) scale(1.1) !important;
}

.tp-search-form-icon svg {
    color: #333 !important;
    width: 24px !important;
    height: 24px !important;
}

/* Glowing effect animation */
@keyframes glow {
    0% {
        box-shadow: 0 15px 40px rgba(196, 238, 24, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1);
        border-color: #C4EE18;
    }
    50% {
        box-shadow: 0 20px 60px rgba(196, 238, 24, 0.6), 0 10px 25px rgba(0, 0, 0, 0.15);
        border-color: #a8d916;
    }
    100% {
        box-shadow: 0 15px 40px rgba(196, 238, 24, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1);
        border-color: #C4EE18;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(196, 238, 24, 0.5), 0 0 20px rgba(196, 238, 24, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 12px 35px rgba(196, 238, 24, 0.7), 0 0 30px rgba(196, 238, 24, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(196, 238, 24, 0.5), 0 0 20px rgba(196, 238, 24, 0.3);
    }
}

.tp-search-form-input {
    animation: glow 4s ease-in-out infinite !important;
}

.tp-search-form-icon {
    animation: pulse 3s ease-in-out infinite !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tp-hero-search-input {
        padding: 15px 50px 15px 20px;
        font-size: 16px;
        min-height: 60px;
    }
    
    .tp-search-form-icon {
        width: 40px;
        height: 40px;
        right: 15px;
    }
    
    .tp-search-form-icon svg {
        width: 16px;
        height: 16px;
    }
}

@media (max-width: 576px) {
    .tp-hero-search-section {
        margin-top: 40px;
    }
    
    .tp-hero-search-input {
        padding: 12px 45px 12px 15px;
        font-size: 14px;
        min-height: 50px;
    }
    
    .tp-search-form-icon {
        width: 35px;
        height: 35px;
        right: 10px;
    }
    
    .tp-search-form-icon svg {
        width: 14px;
        height: 14px;
    }
}

/* Hero Title Centering */
.tp-hero-title.text-center {
    text-align: center !important;
}

/* Hero Bottom Content Centering */
.tp-hero-bottom-content.text-center {
    text-align: center !important;
}

.tp-hero-bottom-content.text-center .tp-hero-customer {
    justify-content: center !important;
}

/* Additional Search Form Styling */
.tp-search-form {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.tp-search-form::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #C4EE18, #a8d916, #C4EE18);
    border-radius: 60px;
    z-index: -1;
    opacity: 0.3;
    animation: rotate 6s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Search section title */
.tp-hero-search-section::before {
    content: '🔍 جستجو کنید';
    display: block;
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    font-family: 'Vazirmatn', sans-serif;
}
