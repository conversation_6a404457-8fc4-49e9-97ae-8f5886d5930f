<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seraj Features Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">Seraj Scientific Database - Feature Tests</h1>
        
        <!-- Language Switch Test -->
        <div class="test-section">
            <h3><i class="fas fa-globe"></i> Language Switch Test</h3>
            <p>Testing enhanced language switching functionality</p>
            <button class="btn btn-primary" onclick="testLanguageSwitch()">Test Language Switch</button>
            <div id="languageTestResult" class="test-result test-pending">
                <i class="fas fa-clock"></i> Test not run yet
            </div>
        </div>

        <!-- Charts Test -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> Charts Test</h3>
            <p>Testing advanced chart visualizations</p>
            <button class="btn btn-primary" onclick="testCharts()">Test Charts</button>
            <div id="chartsTestResult" class="test-result test-pending">
                <i class="fas fa-clock"></i> Test not run yet
            </div>
        </div>

        <!-- Top Rankings Test -->
        <div class="test-section">
            <h3><i class="fas fa-trophy"></i> Top Rankings Test</h3>
            <p>Testing interactive top rankings sections</p>
            <button class="btn btn-primary" onclick="testTopRankings()">Test Top Rankings</button>
            <div id="rankingsTestResult" class="test-result test-pending">
                <i class="fas fa-clock"></i> Test not run yet
            </div>
        </div>

        <!-- Services Test -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> Services Test</h3>
            <p>Testing services and legal information</p>
            <button class="btn btn-primary" onclick="testServices()">Test Services</button>
            <div id="servicesTestResult" class="test-result test-pending">
                <i class="fas fa-clock"></i> Test not run yet
            </div>
        </div>

        <!-- Contact Modal Test -->
        <div class="test-section">
            <h3><i class="fas fa-envelope"></i> Contact Modal Test</h3>
            <p>Testing contact form and modal functionality</p>
            <button class="btn btn-primary" onclick="testContactModal()">Test Contact Modal</button>
            <div id="contactTestResult" class="test-result test-pending">
                <i class="fas fa-clock"></i> Test not run yet
            </div>
        </div>

        <!-- Responsive Design Test -->
        <div class="test-section">
            <h3><i class="fas fa-mobile-alt"></i> Responsive Design Test</h3>
            <p>Testing responsive design across different screen sizes</p>
            <button class="btn btn-primary" onclick="testResponsiveDesign()">Test Responsive Design</button>
            <div id="responsiveTestResult" class="test-result test-pending">
                <i class="fas fa-clock"></i> Test not run yet
            </div>
        </div>

        <!-- Run All Tests -->
        <div class="text-center mt-5">
            <button class="btn btn-success btn-lg" onclick="runAllTests()">
                <i class="fas fa-play"></i> Run All Tests
            </button>
        </div>

        <!-- Test Summary -->
        <div id="testSummary" class="mt-5" style="display: none;">
            <h3>Test Summary</h3>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testResults = {};

        function updateTestResult(testName, passed, message) {
            const resultDiv = document.getElementById(testName + 'TestResult');
            testResults[testName] = passed;
            
            if (passed) {
                resultDiv.className = 'test-result test-pass';
                resultDiv.innerHTML = `<i class="fas fa-check"></i> ${message}`;
            } else {
                resultDiv.className = 'test-result test-fail';
                resultDiv.innerHTML = `<i class="fas fa-times"></i> ${message}`;
            }
        }

        function testLanguageSwitch() {
            try {
                // Test if language switch elements exist
                const languageDropdown = document.querySelector('.language-switcher');
                const languageOptions = document.querySelectorAll('.language-option');
                
                if (languageDropdown && languageOptions.length >= 2) {
                    updateTestResult('language', true, 'Language switch elements found and functional');
                } else {
                    updateTestResult('language', false, 'Language switch elements missing or incomplete');
                }
            } catch (error) {
                updateTestResult('language', false, 'Error testing language switch: ' + error.message);
            }
        }

        function testCharts() {
            try {
                // Test if Chart.js is loaded and charts exist
                if (typeof Chart !== 'undefined') {
                    const chartElements = document.querySelectorAll('canvas[id*="Chart"]');
                    if (chartElements.length >= 4) {
                        updateTestResult('charts', true, `Found ${chartElements.length} chart elements with Chart.js loaded`);
                    } else {
                        updateTestResult('charts', false, `Only found ${chartElements.length} chart elements, expected 4`);
                    }
                } else {
                    updateTestResult('charts', false, 'Chart.js library not loaded');
                }
            } catch (error) {
                updateTestResult('charts', false, 'Error testing charts: ' + error.message);
            }
        }

        function testTopRankings() {
            try {
                const topCards = document.querySelectorAll('.top-card');
                const rankingIndicators = document.querySelectorAll('.ranking-indicator');
                const actionButtons = document.querySelectorAll('.top-card-actions');
                
                if (topCards.length >= 12 && rankingIndicators.length >= 12) {
                    updateTestResult('rankings', true, `Found ${topCards.length} top cards with ranking indicators and actions`);
                } else {
                    updateTestResult('rankings', false, `Insufficient top ranking elements found`);
                }
            } catch (error) {
                updateTestResult('rankings', false, 'Error testing top rankings: ' + error.message);
            }
        }

        function testServices() {
            try {
                const serviceLinks = document.querySelectorAll('.footer-link');
                const socialLinks = document.querySelectorAll('.social-link');
                const contactInfo = document.querySelectorAll('.contact-item');
                
                if (serviceLinks.length >= 6 && socialLinks.length >= 4 && contactInfo.length >= 3) {
                    updateTestResult('services', true, 'All service links, social links, and contact information found');
                } else {
                    updateTestResult('services', false, 'Some service elements are missing');
                }
            } catch (error) {
                updateTestResult('services', false, 'Error testing services: ' + error.message);
            }
        }

        function testContactModal() {
            try {
                const contactModal = document.getElementById('contactModal');
                const contactForm = document.getElementById('contactForm');
                const legalModal = document.getElementById('legalModal');
                
                if (contactModal && contactForm && legalModal) {
                    updateTestResult('contact', true, 'Contact modal and legal modal elements found');
                } else {
                    updateTestResult('contact', false, 'Modal elements missing');
                }
            } catch (error) {
                updateTestResult('contact', false, 'Error testing contact modal: ' + error.message);
            }
        }

        function testResponsiveDesign() {
            try {
                // Test viewport meta tag
                const viewportMeta = document.querySelector('meta[name="viewport"]');
                const bootstrapCSS = document.querySelector('link[href*="bootstrap"]');
                const responsiveClasses = document.querySelectorAll('[class*="col-"], [class*="d-"], [class*="text-"]');
                
                if (viewportMeta && bootstrapCSS && responsiveClasses.length > 0) {
                    updateTestResult('responsive', true, 'Responsive design elements and Bootstrap classes found');
                } else {
                    updateTestResult('responsive', false, 'Responsive design elements missing');
                }
            } catch (error) {
                updateTestResult('responsive', false, 'Error testing responsive design: ' + error.message);
            }
        }

        function runAllTests() {
            testLanguageSwitch();
            setTimeout(() => testCharts(), 500);
            setTimeout(() => testTopRankings(), 1000);
            setTimeout(() => testServices(), 1500);
            setTimeout(() => testContactModal(), 2000);
            setTimeout(() => testResponsiveDesign(), 2500);
            setTimeout(() => showTestSummary(), 3000);
        }

        function showTestSummary() {
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result).length;
            const failedTests = totalTests - passedTests;
            
            const summaryDiv = document.getElementById('testSummary');
            const summaryContent = document.getElementById('summaryContent');
            
            summaryContent.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">${passedTests}</h5>
                                <p class="card-text">Tests Passed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">${failedTests}</h5>
                                <p class="card-text">Tests Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">${totalTests}</h5>
                                <p class="card-text">Total Tests</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: ${(passedTests/totalTests)*100}%" 
                             aria-valuenow="${passedTests}" aria-valuemin="0" aria-valuemax="${totalTests}">
                            ${Math.round((passedTests/totalTests)*100)}%
                        </div>
                    </div>
                </div>
            `;
            
            summaryDiv.style.display = 'block';
        }
    </script>
</body>
</html>
