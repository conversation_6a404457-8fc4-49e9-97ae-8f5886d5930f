import React, { useEffect, useMemo, useRef, useState } from "react";
import Head from "next/head";

// Utility: join class names
function classNames(...classes: Array<string | false | null | undefined>): string {
  return classes.filter(Boolean).join(" ");
}

type Field =
  | "Biology"
  | "Physics"
  | "Chemistry"
  | "Computer Science"
  | "Mathematics"
  | "Medicine"
  | "Earth Science";

type ItemType = "Dataset" | "Paper";

type Item = {
  id: string;
  title: string;
  abstract: string;
  authors: string[];
  year: number;
  field: Field;
  tags: string[];
  openAccess: boolean;
  hasCode: boolean;
  citations: number;
  downloads: number;
  institution: string;
  doi: string;
  type: ItemType;
  updated: string; // ISO date string
};

const SAMPLE_ITEMS: Item[] = [
  {
    id: "1",
    title: "Genome-wide association study identifies novel loci in plant drought tolerance",
    abstract:
      "We present a comprehensive GWAS of drought tolerance across 120 cultivars, identifying loci that modulate stomatal conductance and root architecture.",
    authors: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
    year: 2024,
    field: "Biology",
    tags: ["GWAS", "drought", "genomics"],
    openAccess: true,
    hasCode: true,
    citations: 52,
    downloads: 1832,
    institution: "University of Cambridge",
    doi: "10.1234/scibase.2024.0001",
    type: "Paper",
    updated: "2024-11-21",
  },
  {
    id: "2",
    title: "High-resolution ocean salinity dataset from multi-mission satellites (2002–2023)",
    abstract:
      "An aggregated Level-3 ocean surface salinity dataset derived from SMOS, Aquarius, and SMAP with cross-sensor harmonization.",
    authors: ["C. Romero", "A. Singh"],
    year: 2023,
    field: "Earth Science",
    tags: ["satellite", "salinity", "remote sensing"],
    openAccess: true,
    hasCode: false,
    citations: 19,
    downloads: 7510,
    institution: "NOAA",
    doi: "10.1234/scibase.2023.0412",
    type: "Dataset",
    updated: "2024-07-02",
  },
  {
    id: "3",
    title: "Explainable graph neural networks for protein-ligand binding",
    abstract:
      "We propose an interpretable GNN that highlights substructures pivotal for binding affinity prediction with state-of-the-art performance.",
    authors: ["K. Müller", "R. Shah"],
    year: 2025,
    field: "Computer Science",
    tags: ["GNN", "drug discovery", "XAI"],
    openAccess: false,
    hasCode: true,
    citations: 7,
    downloads: 1290,
    institution: "ETH Zürich",
    doi: "10.1234/scibase.2025.0107",
    type: "Paper",
    updated: "2025-02-10",
  },
  {
    id: "4",
    title: "Large-scale atmospheric chemistry reactions catalogue",
    abstract:
      "A curated dataset of over 25,000 atmospheric reactions with rate constants and uncertainty quantification.",
    authors: ["P. Laurent"],
    year: 2022,
    field: "Chemistry",
    tags: ["atmosphere", "kinetics", "catalogue"],
    openAccess: true,
    hasCode: false,
    citations: 88,
    downloads: 5403,
    institution: "CNRS",
    doi: "10.1234/scibase.2022.0819",
    type: "Dataset",
    updated: "2023-12-17",
  },
  {
    id: "5",
    title: "A unified benchmark for low-shot physics discovery",
    abstract:
      "We introduce a benchmark of synthetic and real experiments to evaluate low-shot discovery of physics laws from data.",
    authors: ["H. Wang", "E. Brown", "D. Kim"],
    year: 2024,
    field: "Physics",
    tags: ["benchmark", "symbolic regression"],
    openAccess: true,
    hasCode: true,
    citations: 34,
    downloads: 2120,
    institution: "Caltech",
    doi: "10.1234/scibase.2024.0221",
    type: "Dataset",
    updated: "2024-09-25",
  },
  {
    id: "6",
    title: "Open clinical notes corpus for cardiovascular risk stratification",
    abstract:
      "De-identified clinical notes paired with outcomes to enable research in cardiovascular risk prediction and interpretability.",
    authors: ["R. Johnson", "S. Rao"],
    year: 2021,
    field: "Medicine",
    tags: ["clinical", "NLP", "cardiology"],
    openAccess: false,
    hasCode: false,
    citations: 140,
    downloads: 4632,
    institution: "Mayo Clinic",
    doi: "10.1234/scibase.2021.0301",
    type: "Dataset",
    updated: "2022-05-04",
  },
  {
    id: "7",
    title: "Mathematical foundations of stable diffusion sampling",
    abstract:
      "We analyze convergence guarantees for stochastic samplers used in diffusion models and propose variance-reduced estimators.",
    authors: ["Y. Li", "J. O'Connor"],
    year: 2023,
    field: "Mathematics",
    tags: ["diffusion", "samplers", "theory"],
    openAccess: true,
    hasCode: true,
    citations: 67,
    downloads: 3289,
    institution: "University of Oxford",
    doi: "10.1234/scibase.2023.0901",
    type: "Paper",
    updated: "2024-02-28",
  },
  {
    id: "8",
    title: "Comprehensive molecular dynamics trajectories of membrane proteins",
    abstract:
      "An open repository of long-timescale MD simulations for membrane proteins with curated metadata and analysis scripts.",
    authors: ["N. Ahmed", "G. Rossi"],
    year: 2020,
    field: "Chemistry",
    tags: ["MD", "proteins", "membranes"],
    openAccess: true,
    hasCode: true,
    citations: 210,
    downloads: 12890,
    institution: "Max Planck Institute",
    doi: "10.1234/scibase.2020.1207",
    type: "Dataset",
    updated: "2021-03-16",
  },
  {
    id: "9",
    title: "Global seismic events catalogue with waveform snippets",
    abstract:
      "A harmonized dataset of seismic events from 1990–2022 with standardized magnitudes and waveform snippets for each event.",
    authors: ["A. Tanaka"],
    year: 2022,
    field: "Earth Science",
    tags: ["seismic", "catalogue", "waveforms"],
    openAccess: true,
    hasCode: false,
    citations: 95,
    downloads: 7842,
    institution: "USGS",
    doi: "10.1234/scibase.2022.0711",
    type: "Dataset",
    updated: "2023-01-20",
  },
  {
    id: "10",
    title: "Efficient transformers for long genomic sequences",
    abstract:
      "We develop an attention mechanism tailored to kilobase-scale DNA sequences that improves efficiency while preserving accuracy.",
    authors: ["D. Park", "S. Kim"],
    year: 2024,
    field: "Computer Science",
    tags: ["transformers", "genomics", "long sequence"],
    openAccess: false,
    hasCode: true,
    citations: 12,
    downloads: 990,
    institution: "KAIST",
    doi: "10.1234/scibase.2024.0613",
    type: "Paper",
    updated: "2024-10-08",
  },
  {
    id: "11",
    title: "Photocatalytic CO2 reduction dataset of polymeric frameworks",
    abstract:
      "A dataset of polymeric photocatalysts and conditions for CO2 reduction with reaction yields and computed descriptors.",
    authors: ["I. Petrov", "L. Zhao"],
    year: 2023,
    field: "Chemistry",
    tags: ["photocatalysis", "CO2", "materials"],
    openAccess: true,
    hasCode: false,
    citations: 31,
    downloads: 2571,
    institution: "Tsinghua University",
    doi: "10.1234/scibase.2023.0522",
    type: "Dataset",
    updated: "2023-11-11",
  },
  {
    id: "12",
    title: "Meta-analysis of randomized trials for hypertension treatments",
    abstract:
      "We synthesize effects from 63 RCTs comparing first-line hypertension therapies, reporting subgroup analyses and adverse events.",
    authors: ["G. Silva", "R. Chen"],
    year: 2021,
    field: "Medicine",
    tags: ["meta-analysis", "RCT", "hypertension"],
    openAccess: true,
    hasCode: false,
    citations: 176,
    downloads: 4120,
    institution: "University of São Paulo",
    doi: "10.1234/scibase.2021.1110",
    type: "Paper",
    updated: "2022-03-12",
  },
  {
    id: "13",
    title: "Mathematical dataset of prime distribution in short intervals",
    abstract:
      "An open dataset with computed statistics of prime counts in short intervals up to 10^12, with conjecture tests.",
    authors: ["T. Eriksson"],
    year: 2019,
    field: "Mathematics",
    tags: ["number theory", "primes"],
    openAccess: true,
    hasCode: true,
    citations: 58,
    downloads: 3871,
    institution: "KTH Royal Institute of Technology",
    doi: "10.1234/scibase.2019.0209",
    type: "Dataset",
    updated: "2020-05-22",
  },
  {
    id: "14",
    title: "Neural PDE solvers for turbulent flow forecasting",
    abstract:
      "A hybrid differentiable solver combining physics constraints with neural operators to forecast turbulent flows.",
    authors: ["S. Gupta", "A. Li"],
    year: 2024,
    field: "Physics",
    tags: ["PDE", "neural operators", "turbulence"],
    openAccess: false,
    hasCode: true,
    citations: 23,
    downloads: 1460,
    institution: "MIT",
    doi: "10.1234/scibase.2024.0777",
    type: "Paper",
    updated: "2025-01-03",
  },
  {
    id: "15",
    title: "Urban air quality sensor network: 10-year longitudinal dataset",
    abstract:
      "Ten years of PM2.5, NO2, and O3 measurements across a dense urban network with sensor calibration metadata.",
    authors: ["R. Williams", "E. Garcia"],
    year: 2018,
    field: "Earth Science",
    tags: ["air quality", "sensors"],
    openAccess: true,
    hasCode: false,
    citations: 320,
    downloads: 18210,
    institution: "Stanford University",
    doi: "10.1234/scibase.2018.0042",
    type: "Dataset",
    updated: "2020-09-19",
  },
  {
    id: "16",
    title: "Open-source pipeline for reproducible neuroimaging",
    abstract:
      "A modular pipeline for preprocessing and statistical analysis of fMRI data with full provenance tracking.",
    authors: ["B. Allen", "Q. Zhou"],
    year: 2022,
    field: "Medicine",
    tags: ["neuroimaging", "fMRI", "reproducibility"],
    openAccess: true,
    hasCode: true,
    citations: 61,
    downloads: 5220,
    institution: "Harvard Medical School",
    doi: "10.1234/scibase.2022.0191",
    type: "Paper",
    updated: "2022-12-02",
  },
  {
    id: "17",
    title: "Quantum materials dataset: topological invariants and band structures",
    abstract:
      "Computed band structures and topological invariants for 5,000 materials with standardized metadata.",
    authors: ["M. Rossi", "P. Chen"],
    year: 2023,
    field: "Physics",
    tags: ["quantum", "materials", "topological"],
    openAccess: true,
    hasCode: true,
    citations: 48,
    downloads: 6540,
    institution: "Lawrence Berkeley National Laboratory",
    doi: "10.1234/scibase.2023.0988",
    type: "Dataset",
    updated: "2024-04-14",
  },
  {
    id: "18",
    title: "Cross-lingual biomedical NER benchmark",
    abstract:
      "A benchmark for named entity recognition across 12 languages with harmonized biomedical ontologies.",
    authors: ["Z. Ahmed", "Y. Park"],
    year: 2024,
    field: "Computer Science",
    tags: ["biomedical", "NER", "multilingual"],
    openAccess: true,
    hasCode: true,
    citations: 15,
    downloads: 2410,
    institution: "University of Toronto",
    doi: "10.1234/scibase.2024.0088",
    type: "Dataset",
    updated: "2024-12-10",
  },
];

// Accent palette for academic vibe
const ACCENTS = {
  indigo: {
    name: "Indigo",
    text: "text-indigo-600",
    bg: "bg-indigo-600",
    hoverBg: "hover:bg-indigo-700",
    ring: "focus:ring-indigo-600",
    border: "border-indigo-600",
    badgeBg: "bg-indigo-50",
  },
  blue: {
    name: "Blue",
    text: "text-blue-600",
    bg: "bg-blue-600",
    hoverBg: "hover:bg-blue-700",
    ring: "focus:ring-blue-600",
    border: "border-blue-600",
    badgeBg: "bg-blue-50",
  },
  emerald: {
    name: "Emerald",
    text: "text-emerald-600",
    bg: "bg-emerald-600",
    hoverBg: "hover:bg-emerald-700",
    ring: "focus:ring-emerald-600",
    border: "border-emerald-600",
    badgeBg: "bg-emerald-50",
  },
  violet: {
    name: "Violet",
    text: "text-violet-600",
    bg: "bg-violet-600",
    hoverBg: "hover:bg-violet-700",
    ring: "focus:ring-violet-600",
    border: "border-violet-600",
    badgeBg: "bg-violet-50",
  },
};

type AccentKey = keyof typeof ACCENTS;

function useLocalStorage<T>(key: string, initial: T) {
  const [state, setState] = useState<T>(initial);
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    try {
      const raw = typeof window !== "undefined" ? window.localStorage.getItem(key) : null;
      if (raw) setState(JSON.parse(raw));
    } catch {}
    setHydrated(true);
  }, [key]);

  useEffect(() => {
    if (!hydrated) return;
    try {
      window.localStorage.setItem(key, JSON.stringify(state));
    } catch {}
  }, [key, state, hydrated]);

  return [state, setState, hydrated] as const;
}

function StarIcon({ filled = false, className = "" }: { filled?: boolean; className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill={filled ? "currentColor" : "none"} stroke="currentColor">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.173c.969 0 1.371 1.24.588 1.81l-3.377 2.455a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.377-2.454a1 1 0 00-1.176 0l-3.377 2.454c-.784.57-1.838-.196-1.539-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.052 9.394c-.783-.57-.38-1.81.588-1.81h4.173a1 1 0 00.95-.69l1.286-3.967z"
      />
    </svg>
  );
}

function BookIcon({ className = "" }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v12M8 6h8a2 2 0 012 2v10a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
    </svg>
  );
}

function FilterIcon({ className = "" }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h18M6 12h12M9 20h6" />
    </svg>
  );
}

function CopyIcon({ className = "" }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
      <path strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" d="M8 8h8v8H8z" />
      <path strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" d="M16 8V6a2 2 0 00-2-2H8a2 2 0 00-2 2v6a2 2 0 002 2h2" />
    </svg>
  );
}

function Tag({ children, tone = "slate", className = "" }: { children: React.ReactNode; tone?: "slate" | "amber" | "emerald" | "indigo"; className?: string }) {
  const toneMap: Record<string, string> = {
    slate: "bg-slate-100 text-slate-700",
    amber: "bg-amber-100 text-amber-800",
    emerald: "bg-emerald-100 text-emerald-800",
    indigo: "bg-indigo-100 text-indigo-800",
  };
  return (
    <span className={classNames("inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium", toneMap[tone], className)}>
      {children}
    </span>
  );
}

function highlight(text: string, query: string, accentTextClass: string) {
  if (!query.trim()) return text;
  try {
    const esc = query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const parts = text.split(new RegExp(`(${esc})`, "ig"));
    return (
      <>
        {parts.map((part, i) =>
          part.toLowerCase() === query.toLowerCase() ? (
            <mark key={i} className={classNames("bg-transparent", accentTextClass)}>
              {part}
            </mark>
          ) : (
            <span key={i}>{part}</span>
          )
        )}
      </>
    );
  } catch {
    return text;
  }
}

type SortKey = "relevance" | "year" | "citations" | "downloads";

type ViewMode = "grid" | "list";

type Toast = { id: number; message: string };

export default function HomePage() {
  const [query, setQuery] = useState("");
  const [selectedFields, setSelectedFields] = useState<Field[]>([]);
  const [openAccessOnly, setOpenAccessOnly] = useState(false);
  const [hasCodeOnly, setHasCodeOnly] = useState(false);
  const [typeFilter, setTypeFilter] = useState<ItemType | "All">("All");
  const [sortKey, setSortKey] = useState<SortKey>("relevance");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [accent, setAccent] = useState<AccentKey>("indigo");
  const [favorites, setFavorites] = useLocalStorage<string[]>("scibase:favs", []);
  const [recentSearches, setRecentSearches] = useLocalStorage<string[]>("scibase:recent", []);
  const [showFilters, setShowFilters] = useState(true);
  const [onlyFavorites, setOnlyFavorites] = useState(false);
  const [previewItem, setPreviewItem] = useState<Item | null>(null);
  const [toasts, setToasts] = useState<Toast[]>([]);
  const [, , hydratedFavs] = useLocalStorage<string[]>("scibase:favs", []); // For hydration status

  const searchRef = useRef<HTMLInputElement>(null);
  const acc = ACCENTS[accent];

  // Keyboard shortcuts: '/' focus search, 'Esc' closes preview
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if (e.key === "/" && !e.metaKey && !e.ctrlKey && !e.altKey) {
        e.preventDefault();
        searchRef.current?.focus();
      }
      if (e.key === "Escape") {
        setPreviewItem(null);
      }
    };
    window.addEventListener("keydown", handler);
    return () => window.removeEventListener("keydown", handler);
  }, []);

  const toggleField = (f: Field) => {
    setSelectedFields((prev) => (prev.includes(f) ? prev.filter((x) => x !== f) : [...prev, f]));
  };

  const isFavorite = (id: string) => favorites.includes(id);
  const toggleFavorite = (id: string) => {
    setFavorites((prev) => (prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]));
  };

  const addToast = (message: string) => {
    const id = Date.now();
    setToasts((t) => [...t, { id, message }]);
    setTimeout(() => setToasts((t) => t.filter((x) => x.id !== id)), 2000);
  };

  const fields: Field[] = [
    "Biology",
    "Physics",
    "Chemistry",
    "Computer Science",
    "Mathematics",
    "Medicine",
    "Earth Science",
  ];

  const filtered = useMemo(() => {
    const q = query.trim().toLowerCase();
    let results = SAMPLE_ITEMS.filter((item) => {
      if (typeFilter !== "All" && item.type !== typeFilter) return false;
      if (openAccessOnly && !item.openAccess) return false;
      if (hasCodeOnly && !item.hasCode) return false;
      if (selectedFields.length && !selectedFields.includes(item.field)) return false;
      if (onlyFavorites && !favorites.includes(item.id)) return false;
      if (!q) return true;
      const hay = [item.title, item.abstract, item.authors.join(" "), item.tags.join(" ")].join(" ").toLowerCase();
      return hay.includes(q);
    });

    switch (sortKey) {
      case "year":
        results = [...results].sort((a, b) => b.year - a.year);
        break;
      case "citations":
        results = [...results].sort((a, b) => b.citations - a.citations);
        break;
      case "downloads":
        results = [...results].sort((a, b) => b.downloads - a.downloads);
        break;
      default:
        // Relevance: naive scoring by presence in title > abstract > authors/tags
        if (q) {
          const score = (it: Item) => {
            let s = 0;
            const LQ = q.toLowerCase();
            if (it.title.toLowerCase().includes(LQ)) s += 3;
            if (it.abstract.toLowerCase().includes(LQ)) s += 2;
            if (it.authors.join(" ").toLowerCase().includes(LQ)) s += 1;
            if (it.tags.join(" ").toLowerCase().includes(LQ)) s += 1;
            s += Math.min(2, Math.floor(it.citations / 100));
            return s;
          };
          results = [...results].sort((a, b) => score(b) - score(a));
        }
        break;
    }

    return results;
  }, [query, selectedFields, openAccessOnly, hasCodeOnly, typeFilter, onlyFavorites, favorites, sortKey]);

  const submitSearch = () => {
    const q = query.trim();
    if (!q) return;
    setRecentSearches((prev) => {
      const next = [q, ...prev.filter((x) => x !== q)].slice(0, 6);
      return next;
    });
  };

  const clearFilters = () => {
    setSelectedFields([]);
    setOpenAccessOnly(false);
    setHasCodeOnly(false);
    setTypeFilter("All");
    setOnlyFavorites(false);
    setSortKey("relevance");
  };

  const copyDOI = async (doi: string) => {
    try {
      await navigator.clipboard.writeText(doi);
      addToast("DOI copied");
    } catch {
      addToast("Copy failed");
    }
  };

  return (
    <>
      <Head>
        <title>SciBase — Modern Scientific Database</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        {/* Header */}
        <header className="sticky top-0 z-10 backdrop-blur bg-white/70 border-b border-slate-200">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between py-4">
              <div className="flex items-center gap-3">
                <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
                <div>
                  <div className={classNames("text-xl font-semibold", acc.text)}>SciBase</div>
                  <p className="text-sm text-slate-500">Discover. Reproduce. Cite.</p>
                </div>
              </div>
              <nav className="hidden md:flex items-center gap-6 text-sm text-slate-700">
                <a className="hover:text-slate-900" href="#">Browse</a>
                <a className="hover:text-slate-900" href="#">Collections</a>
                <a className="hover:text-slate-900" href="#">Institutions</a>
                <a className="hover:text-slate-900" href="#">About</a>
                <button
                  className={classNames(
                    "inline-flex items-center rounded-lg px-3 py-2 text-white shadow",
                    acc.bg,
                    acc.hoverBg
                  )}
                  onClick={() => addToast("Sign-in is a placeholder")}
                >
                  Sign in
                </button>
              </nav>
            </div>
          </div>
        </header>

        {/* Hero + Search */}
        <section className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-8">
          <div className="flex flex-col lg:flex-row gap-8 items-start">
            <div className="flex-1 w-full">
              <div className="mb-6">
                <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Explore the world\'s scientific knowledge</h1>
                <p className="text-slate-600 mt-2">Search peer-reviewed papers and curated datasets across disciplines.</p>
              </div>

              {/* Neumorphic search box */}
              <div className="bg-white rounded-2xl p-4 shadow-inner ring-1 ring-slate-200">
                <div className="flex items-center gap-3">
                  <BookIcon className={classNames("w-6 h-6", acc.text)} />
                  <input
                    ref={searchRef}
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") submitSearch();
                    }}
                    placeholder="Search datasets, papers, authors… (Press / to focus)"
                    className={classNames(
                      "flex-1 bg-transparent outline-none placeholder-slate-400 text-slate-800 px-1 py-2"
                    )}
                  />
                  <button
                    onClick={() => setShowFilters((s) => !s)}
                    className={classNames(
                      "inline-flex items-center gap-2 rounded-xl px-3 py-2 text-slate-700 border shadow-sm",
                      "bg-white hover:bg-slate-50",
                      acc.ring,
                      "focus:outline-none focus:ring-2 border-slate-200"
                    )}
                    aria-expanded={showFilters}
                  >
                    <FilterIcon className="w-5 h-5" />
                    Filters
                  </button>
                  <button
                    onClick={submitSearch}
                    className={classNames("rounded-xl px-4 py-2 text-white shadow", acc.bg, acc.hoverBg)}
                  >
                    Search
                  </button>
                </div>

                {/* Quick filters */}
                {showFilters && (
                  <div className="mt-4 border-t border-slate-200 pt-4">
                    <div className="flex flex-wrap gap-2">
                      {fields.map((f) => (
                        <button
                          key={f}
                          onClick={() => toggleField(f)}
                          className={classNames(
                            "px-3 py-1 rounded-full text-sm shadow-sm border",
                            selectedFields.includes(f)
                              ? classNames(acc.bg, "text-white border-transparent")
                              : "bg-white text-slate-700 border-slate-200 hover:bg-slate-50"
                          )}
                        >
                          {f}
                        </button>
                      ))}
                    </div>

                    <div className="mt-4 flex flex-wrap gap-3 items-center">
                      <label className="inline-flex items-center gap-2 text-sm text-slate-700">
                        <input
                          type="checkbox"
                          className="rounded border-slate-300 text-indigo-600 focus:ring-indigo-600"
                          checked={openAccessOnly}
                          onChange={(e) => setOpenAccessOnly(e.target.checked)}
                        />
                        Open Access
                      </label>
                      <label className="inline-flex items-center gap-2 text-sm text-slate-700">
                        <input
                          type="checkbox"
                          className="rounded border-slate-300 text-indigo-600 focus:ring-indigo-600"
                          checked={hasCodeOnly}
                          onChange={(e) => setHasCodeOnly(e.target.checked)}
                        />
                        Has Code
                      </label>

                      <div className="flex items-center gap-2 text-sm">
                        <span className="text-slate-600">Type</span>
                        <div className="flex rounded-xl overflow-hidden border border-slate-200 shadow-sm">
                          {["All", "Dataset", "Paper"].map((t) => (
                            <button
                              key={t}
                              onClick={() => setTypeFilter(t as ItemType | "All")}
                              className={classNames(
                                "px-3 py-1",
                                t === typeFilter ? classNames(acc.bg, "text-white") : "bg-white text-slate-700 hover:bg-slate-50"
                              )}
                            >
                              {t}
                            </button>
                          ))}
                        </div>
                      </div>

                      <div className="ml-auto flex items-center gap-3 w-full md:w-auto">
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-slate-600">Sort</span>
                          <select
                            value={sortKey}
                            onChange={(e) => setSortKey(e.target.value as SortKey)}
                            className="rounded-lg border border-slate-200 bg-white px-2 py-1 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-600"
                          >
                            <option value="relevance">Relevance</option>
                            <option value="year">Year</option>
                            <option value="citations">Citations</option>
                            <option value="downloads">Downloads</option>
                          </select>
                        </div>
                        <div className="flex items-center gap-1 text-sm" role="group" aria-label="View mode">
                          <button
                            onClick={() => setViewMode("grid")}
                            className={classNames(
                              "px-3 py-1 rounded-l-lg border border-slate-200 shadow-sm",
                              viewMode === "grid" ? classNames(acc.bg, "text-white") : "bg-white text-slate-700 hover:bg-slate-50"
                            )}
                          >
                            Grid
                          </button>
                          <button
                            onClick={() => setViewMode("list")}
                            className={classNames(
                              "px-3 py-1 rounded-r-lg border border-slate-200 shadow-sm border-l-0",
                              viewMode === "list" ? classNames(acc.bg, "text-white") : "bg-white text-slate-700 hover:bg-slate-50"
                            )}
                          >
                            List
                          </button>
                        </div>
                      </div>

                      {/* Accent switcher */}
                      <div className="w-full md:w-auto flex items-center gap-2 text-sm">
                        <span className="text-slate-600">Accent</span>
                        <div className="flex overflow-hidden rounded-xl border border-slate-200 shadow-sm">
                          {(Object.keys(ACCENTS) as AccentKey[]).map((k) => (
                            <button
                              key={k}
                              aria-label={ACCENTS[k].name}
                              onClick={() => setAccent(k)}
                              className={classNames(
                                "px-3 py-1",
                                accent === k ? classNames(ACCENTS[k].bg, "text-white") : "bg-white text-slate-700 hover:bg-slate-50"
                              )}
                            >
                              {ACCENTS[k].name}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Recent searches & favorites toggle */}
                    <div className="mt-4 flex flex-wrap items-center gap-3">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-slate-600">Recent:</span>
                        <div className="flex flex-wrap gap-2">
                          {recentSearches.length === 0 && (
                            <span className="text-sm text-slate-400">No recent searches</span>
                          )}
                          {recentSearches.map((r) => (
                            <button
                              key={r}
                              onClick={() => setQuery(r)}
                              className="px-2 py-1 rounded-full text-sm bg-slate-100 text-slate-700 hover:bg-slate-200"
                            >
                              {r}
                            </button>
                          ))}
                        </div>
                      </div>
                      <div className="ml-auto flex items-center gap-3">
                        <label className="inline-flex items-center gap-2 text-sm text-slate-700">
                          <input
                            type="checkbox"
                            className="rounded border-slate-300 text-indigo-600 focus:ring-indigo-600"
                            checked={onlyFavorites}
                            onChange={(e) => setOnlyFavorites(e.target.checked)}
                          />
                          Only favorites
                        </label>
                        <button
                          onClick={clearFilters}
                          className="px-3 py-1 rounded-lg text-sm border border-slate-200 bg-white text-slate-700 shadow-sm hover:bg-slate-50"
                        >
                          Reset
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Side stats panel with neumorphism vibe */}
            <aside className="w-full lg:w-80">
              <div className="bg-white rounded-2xl p-5 shadow-inner ring-1 ring-slate-200">
                <h2 className="text-sm font-semibold text-slate-700">Overview</h2>
                <div className="mt-4 grid grid-cols-2 gap-3">
                  <div className="bg-white rounded-xl p-3 shadow border border-slate-200">
                    <div className="text-xs text-slate-500">Results</div>
                    <div className="text-xl font-semibold text-slate-800">{filtered.length}</div>
                  </div>
                  <div className="bg-white rounded-xl p-3 shadow border border-slate-200">
                    <div className="text-xs text-slate-500">Favorites</div>
                    <div className="text-xl font-semibold text-slate-800">{favorites.length}</div>
                  </div>
                  <div className="bg-white rounded-xl p-3 shadow border border-slate-200">
                    <div className="text-xs text-slate-500">Open Access</div>
                    <div className="text-xl font-semibold text-slate-800">{SAMPLE_ITEMS.filter(i => i.openAccess).length}</div>
                  </div>
                  <div className="bg-white rounded-xl p-3 shadow border border-slate-200">
                    <div className="text-xs text-slate-500">With Code</div>
                    <div className="text-xl font-semibold text-slate-800">{SAMPLE_ITEMS.filter(i => i.hasCode).length}</div>
                  </div>
                </div>
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-slate-700">Featured collection</h3>
                  <div className="mt-3 flex items-start gap-3">
                    <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
                    <div>
                      <div className="font-medium text-slate-800">Climate & Earth</div>
                      <p className="text-sm text-slate-600">Curated datasets for climate change research.</p>
                      <button
                        className={classNames("mt-2 text-sm rounded-lg px-3 py-1 text-white shadow", acc.bg, acc.hoverBg)}
                        onClick={() => {
                          setSelectedFields(["Earth Science"]);
                          addToast("Applied collection filter");
                        }}
                      >
                        Apply filters
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </aside>
          </div>
        </section>

        {/* Results */}
        <main className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 mt-8 pb-16">
          {filtered.length === 0 ? (
            <div className="bg-white rounded-2xl p-8 shadow-inner ring-1 ring-slate-200 text-center">
              <div className="text-lg font-medium text-slate-800">No results</div>
              <p className="text-slate-600 mt-2">Try adjusting filters or using different keywords.</p>
            </div>
          ) : viewMode === "grid" ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
              {filtered.map((item) => (
                <article key={item.id} className="bg-white rounded-2xl p-5 shadow border border-slate-200 hover:shadow-lg transition">
                  <div className="flex items-start justify-between">
                    <Tag tone="slate">{item.field}</Tag>
                    <button
                      aria-label="Toggle favorite"
                      onClick={() => toggleFavorite(item.id)}
                      className={classNames(
                        "p-2 rounded-xl border shadow-sm",
                        isFavorite(item.id) ? classNames(acc.badgeBg, acc.text, acc.border, "border") : "bg-white text-slate-600 border-slate-200 hover:bg-slate-50"
                      )}
                    >
                      <StarIcon className="w-5 h-5" filled={isFavorite(item.id)} />
                    </button>
                  </div>

                  <h3 className="mt-3 text-lg font-semibold text-slate-900">
                    {highlight(item.title, query, acc.text)}
                  </h3>
                  <p className="mt-2 text-sm text-slate-600 line-clamp-3">
                    {highlight(item.abstract, query, acc.text)}
                  </p>

                  <div className="mt-3 flex flex-wrap gap-2">
                    <Tag tone="indigo">{item.type}</Tag>
                    {item.openAccess && <Tag tone="emerald">Open Access</Tag>}
                    {item.hasCode && <Tag tone="amber">Code</Tag>}
                  </div>

                  <div className="mt-4 flex flex-wrap items-center gap-3 text-sm text-slate-600">
                    <span>{item.year}</span>
                    <span>•</span>
                    <span>{item.authors.slice(0, 2).join(", ")}{item.authors.length > 2 ? " et al." : ""}</span>
                    <span>•</span>
                    <span>{item.institution}</span>
                  </div>

                  <div className="mt-4 flex flex-wrap gap-2">
                    <span className="text-xs text-slate-600 bg-slate-100 px-2 py-1 rounded">{item.citations} citations</span>
                    <span className="text-xs text-slate-600 bg-slate-100 px-2 py-1 rounded">{item.downloads} downloads</span>
                  </div>

                  <div className="mt-5 flex flex-wrap gap-2">
                    <button
                      onClick={() => setPreviewItem(item)}
                      className={classNames("px-3 py-2 rounded-lg text-sm text-white shadow", acc.bg, acc.hoverBg)}
                    >
                      Quick preview
                    </button>
                    <button
                      onClick={() => copyDOI(item.doi)}
                      className="px-3 py-2 rounded-lg text-sm border border-slate-200 bg-white text-slate-700 shadow-sm hover:bg-slate-50 inline-flex items-center gap-2"
                    >
                      <CopyIcon className="w-4 h-4" /> Copy DOI
                    </button>
                  </div>
                </article>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-2xl p-4 shadow-inner ring-1 ring-slate-200">
              <ul className="divide-y divide-slate-200">
                {filtered.map((item) => (
                  <li key={item.id} className="py-4">
                    <div className="flex gap-4 items-start">
                      <div className="hidden sm:block">
                        <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center gap-2">
                              <Tag tone="slate">{item.field}</Tag>
                              {item.openAccess && <Tag tone="emerald">Open Access</Tag>}
                              {item.hasCode && <Tag tone="amber">Code</Tag>}
                            </div>
                            <h3 className="mt-2 text-lg font-semibold text-slate-900">{highlight(item.title, query, acc.text)}</h3>
                            <p className="mt-1 text-sm text-slate-600">{highlight(item.abstract, query, acc.text)}</p>
                            <div className="mt-2 flex flex-wrap items-center gap-2 text-sm text-slate-600">
                              <span>{item.type}</span>
                              <span>•</span>
                              <span>{item.year}</span>
                              <span>•</span>
                              <span>{item.authors.slice(0, 2).join(", ")}{item.authors.length > 2 ? " et al." : ""}</span>
                              <span>•</span>
                              <span>{item.citations} cites</span>
                              <span>•</span>
                              <span>{item.downloads} dl</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              aria-label="Toggle favorite"
                              onClick={() => toggleFavorite(item.id)}
                              className={classNames(
                                "p-2 rounded-xl border shadow-sm",
                                isFavorite(item.id) ? classNames(acc.badgeBg, acc.text, acc.border, "border") : "bg-white text-slate-600 border-slate-200 hover:bg-slate-50"
                              )}
                            >
                              <StarIcon className="w-5 h-5" filled={isFavorite(item.id)} />
                            </button>
                            <button
                              onClick={() => setPreviewItem(item)}
                              className={classNames("px-3 py-2 rounded-lg text-sm text-white shadow", acc.bg, acc.hoverBg)}
                            >
                              Preview
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </main>

        {/* Preview Modal */}
        {previewItem && (
          <div className="fixed inset-0 z-20 flex items-center justify-center p-4">
            <div className="absolute inset-0 bg-slate-900/50" onClick={() => setPreviewItem(null)} />
            <div className="relative bg-white rounded-2xl w-full max-w-3xl p-6 shadow-lg ring-1 ring-slate-200">
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <Tag tone="slate">{previewItem.field}</Tag>
                    {previewItem.openAccess && <Tag tone="emerald">Open Access</Tag>}
                    {previewItem.hasCode && <Tag tone="amber">Code</Tag>}
                  </div>
                  <h3 className="mt-2 text-xl font-semibold text-slate-900">{previewItem.title}</h3>
                  <div className="mt-1 text-sm text-slate-600">
                    {previewItem.authors.join(", ")} • {previewItem.institution} • {previewItem.year}
                  </div>
                </div>
                <button
                  onClick={() => setPreviewItem(null)}
                  className="ml-4 px-3 py-1 rounded-lg border border-slate-200 bg-white text-slate-700 shadow-sm hover:bg-slate-50"
                >
                  Close
                </button>
              </div>
              <p className="mt-4 text-slate-700">{previewItem.abstract}</p>

              <div className="mt-4 flex flex-wrap gap-2">
                {previewItem.tags.map((t) => (
                  <span key={t} className="text-xs text-slate-700 bg-slate-100 px-2 py-1 rounded">
                    #{t}
                  </span>
                ))}
              </div>

              <div className="mt-6 flex flex-wrap gap-2">
                <button
                  onClick={() => toggleFavorite(previewItem.id)}
                  className={classNames("px-3 py-2 rounded-lg text-sm border shadow-sm", isFavorite(previewItem.id) ? classNames(acc.badgeBg, acc.text, acc.border, "border") : "bg-white text-slate-700 border-slate-200 hover:bg-slate-50")}
                >
                  {isFavorite(previewItem.id) ? "Remove favorite" : "Add to favorites"}
                </button>
                <button
                  onClick={() => copyDOI(previewItem.doi)}
                  className="px-3 py-2 rounded-lg text-sm border border-slate-200 bg-white text-slate-700 shadow-sm hover:bg-slate-50 inline-flex items-center gap-2"
                >
                  <CopyIcon className="w-4 h-4" /> {previewItem.doi}
                </button>
                <a
                  href="#"
                  className={classNames("px-3 py-2 rounded-lg text-sm text-white shadow", acc.bg, acc.hoverBg)}
                  onClick={(e) => {
                    e.preventDefault();
                    addToast("Download is a placeholder");
                  }}
                >
                  Download
                </a>
              </div>
            </div>
          </div>
        )}

        {/* Toasts */}
        <div className="fixed bottom-4 right-4 z-30 space-y-2">
          {toasts.map((t) => (
            <div key={t.id} className="bg-slate-800 text-white rounded-lg px-4 py-2 shadow">
              {t.message}
            </div>
          ))}
        </div>

        {/* Footer */}
        <footer className="mt-16 border-t border-slate-200">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8 text-sm text-slate-600 flex flex-col sm:flex-row items-center justify-between">
            <div>&copy; {new Date().getFullYear()} SciBase. All rights reserved.</div>
            <div className="flex items-center gap-4 mt-4 sm:mt-0">
              <a href="#" className="hover:text-slate-900">Privacy</a>
              <a href="#" className="hover:text-slate-900">Terms</a>
              <a href="#" className="hover:text-slate-900">Contact</a>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
